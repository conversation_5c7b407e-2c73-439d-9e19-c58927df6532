﻿using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using SupplierManagementSystem.Data;
using SupplierManagementSystem.Models;
using SupplierManagementSystem.Services;
using SupplierManagementSystem.Views;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Threading;

namespace SupplierManagementSystem;

/// <summary>
/// النافذة الرئيسية لنظام إدارة الموردين
/// Main Window for Supplier Management System
/// </summary>
public partial class MainWindow : Window
{
    private readonly SupplierDbContext _context;
    private readonly SupplierService _supplierService;
    private readonly DispatcherTimer _timer;
    private readonly IConfiguration _configuration;

    public MainWindow()
    {
        InitializeComponent();

        // إعداد التكوين
        _configuration = BuildConfiguration();

        // إعداد قاعدة البيانات
        _context = CreateDbContext();
        _supplierService = new SupplierService(_context);

        // إعداد المؤقت لتحديث الوقت
        _timer = new DispatcherTimer();
        _timer.Interval = TimeSpan.FromSeconds(1);
        _timer.Tick += Timer_Tick;
        _timer.Start();

        // تحميل البيانات الأولية
        Loaded += MainWindow_Loaded;
    }

    private IConfiguration BuildConfiguration()
    {
        var builder = new ConfigurationBuilder()
            .SetBasePath(Directory.GetCurrentDirectory())
            .AddJsonFile("appsettings.json", optional: false, reloadOnChange: true);

        return builder.Build();
    }

    private SupplierDbContext CreateDbContext()
    {
        var connectionString = _configuration.GetConnectionString("DefaultConnection");
        var optionsBuilder = new DbContextOptionsBuilder<SupplierDbContext>();
        optionsBuilder.UseSqlServer(connectionString);

        return new SupplierDbContext(optionsBuilder.Options);
    }

    private async void MainWindow_Loaded(object sender, RoutedEventArgs e)
    {
        try
        {
            // التأكد من إنشاء قاعدة البيانات
            await _context.Database.EnsureCreatedAsync();

            // تحميل الإحصائيات
            await LoadDashboardDataAsync();

            // تحميل آخر النشاطات
            await LoadRecentActivitiesAsync();

            StatusText.Text = "متصل";
        }
        catch (Exception ex)
        {
            MessageBox.Show($"خطأ في تحميل البيانات: {ex.Message}", "خطأ",
                MessageBoxButton.OK, MessageBoxImage.Error);
            StatusText.Text = "خطأ في الاتصال";
        }
    }

    private void Timer_Tick(object sender, EventArgs e)
    {
        CurrentTimeText.Text = DateTime.Now.ToString("yyyy/MM/dd HH:mm:ss");
    }

    private async Task LoadDashboardDataAsync()
    {
        try
        {
            var statistics = await _supplierService.GetSupplierStatisticsAsync();
            var expiringDocs = await _supplierService.GetExpiringDocumentsAsync(30);

            // تحديث الإحصائيات
            var totalSuppliers = statistics.Values.Sum();
            TotalSuppliersText.Text = totalSuppliers.ToString();

            ApprovedSuppliersText.Text = statistics.ContainsKey("معتمد") ?
                statistics["معتمد"].ToString() : "0";

            PendingSuppliersText.Text = statistics.ContainsKey("قيد المراجعة") ?
                statistics["قيد المراجعة"].ToString() : "0";

            ExpiringDocumentsText.Text = expiringDocs.Count.ToString();
        }
        catch (Exception ex)
        {
            MessageBox.Show($"خطأ في تحميل الإحصائيات: {ex.Message}", "خطأ",
                MessageBoxButton.OK, MessageBoxImage.Warning);
        }
    }

    private async Task LoadRecentActivitiesAsync()
    {
        try
        {
            var activities = await _supplierService.GetRecentActivitiesAsync(10);
            RecentActivitiesListView.ItemsSource = activities;
        }
        catch (Exception ex)
        {
            MessageBox.Show($"خطأ في تحميل النشاطات: {ex.Message}", "خطأ",
                MessageBoxButton.OK, MessageBoxImage.Warning);
        }
    }

    // أحداث أزرار القائمة العلوية
    private async void BtnDashboard_Click(object sender, RoutedEventArgs e)
    {
        ShowPanel("Dashboard");
        await LoadDashboardDataAsync();
        await LoadRecentActivitiesAsync();
    }

    private void BtnSuppliers_Click(object sender, RoutedEventArgs e)
    {
        ShowPanel("Suppliers");
        // سيتم تنفيذ هذا لاحقاً
    }

    private void BtnAccreditation_Click(object sender, RoutedEventArgs e)
    {
        ShowPanel("Accreditation");
        // سيتم تنفيذ هذا لاحقاً
    }

    private void BtnReports_Click(object sender, RoutedEventArgs e)
    {
        ShowPanel("Reports");
        // سيتم تنفيذ هذا لاحقاً
    }

    private void BtnSettings_Click(object sender, RoutedEventArgs e)
    {
        ShowPanel("Settings");
        // سيتم تنفيذ هذا لاحقاً
    }

    // أحداث الإجراءات السريعة
    private void BtnAddSupplier_Click(object sender, RoutedEventArgs e)
    {
        try
        {
            var addSupplierWindow = new AddSupplierWindow(_supplierService);
            addSupplierWindow.Owner = this;
            var result = addSupplierWindow.ShowDialog();

            if (result == true)
            {
                // تحديث الإحصائيات بعد إضافة مورد جديد
                _ = LoadDashboardDataAsync();
                _ = LoadRecentActivitiesAsync();

                MessageBox.Show("تم إضافة المورد بنجاح وتحديث الإحصائيات!", "نجح الحفظ",
                    MessageBoxButton.OK, MessageBoxImage.Information);
            }
        }
        catch (Exception ex)
        {
            MessageBox.Show($"خطأ في فتح نافذة إضافة المورد: {ex.Message}", "خطأ",
                MessageBoxButton.OK, MessageBoxImage.Error);
        }
    }

    private void BtnSearchSuppliers_Click(object sender, RoutedEventArgs e)
    {
        ShowPanel("Suppliers");
        // سيتم تنفيذ البحث لاحقاً
    }

    private void BtnReviewRequests_Click(object sender, RoutedEventArgs e)
    {
        ShowPanel("Accreditation");
        // سيتم تنفيذ مراجعة الطلبات لاحقاً
    }

    // دالة لإظهار اللوحة المطلوبة
    private void ShowPanel(string panelName)
    {
        // إخفاء جميع اللوحات
        DashboardPanel.Visibility = Visibility.Collapsed;
        SuppliersPanel.Visibility = Visibility.Collapsed;
        AccreditationPanel.Visibility = Visibility.Collapsed;
        ReportsPanel.Visibility = Visibility.Collapsed;
        SettingsPanel.Visibility = Visibility.Collapsed;

        // إظهار اللوحة المطلوبة
        switch (panelName)
        {
            case "Dashboard":
                DashboardPanel.Visibility = Visibility.Visible;
                break;
            case "Suppliers":
                SuppliersPanel.Visibility = Visibility.Visible;
                break;
            case "Accreditation":
                AccreditationPanel.Visibility = Visibility.Visible;
                break;
            case "Reports":
                ReportsPanel.Visibility = Visibility.Visible;
                break;
            case "Settings":
                SettingsPanel.Visibility = Visibility.Visible;
                break;
        }
    }

    protected override void OnClosed(EventArgs e)
    {
        _timer?.Stop();
        _context?.Dispose();
        base.OnClosed(e);
    }
}