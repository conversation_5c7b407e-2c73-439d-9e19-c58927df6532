﻿using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using SupplierManagementSystem.Data;
using SupplierManagementSystem.Models;
using SupplierManagementSystem.Services;
using SupplierManagementSystem.Views;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Threading;
using System.Windows.Media;
using System.Windows.Documents;
using static SupplierManagementSystem.Services.LoggingService;

namespace SupplierManagementSystem;

/// <summary>
/// النافذة الرئيسية لنظام إدارة الموردين
/// Main Window for Supplier Management System
/// </summary>
public partial class MainWindow : Window
{
    private readonly SupplierDbContext _context;
    private readonly SupplierService _supplierService;
    private readonly DispatcherTimer _timer;
    private readonly IConfiguration _configuration;
    private readonly LoggingService _logger;

    public MainWindow()
    {
        InitializeComponent();

        // إعداد نظام التسجيل
        _logger = new LoggingService();
        _logger.LogInfo("بدء تشغيل نظام إدارة الموردين", "MainWindow");

        try
        {
            // إعداد التكوين
            _configuration = BuildConfiguration();

            // إعداد قاعدة البيانات
            _context = CreateDbContext();
            _supplierService = new SupplierService(_context);

            // إعداد المؤقت لتحديث الوقت
            _timer = new DispatcherTimer();
            _timer.Interval = TimeSpan.FromSeconds(1);
            _timer.Tick += Timer_Tick;
            _timer.Start();

            // تحميل البيانات الأولية
            Loaded += MainWindow_Loaded;

            _logger.LogInfo("تم تهيئة النظام بنجاح", "MainWindow");
        }
        catch (Exception ex)
        {
            _logger?.LogCritical("فشل في تهيئة النظام", ex, "MainWindow");
            MessageBox.Show($"خطأ حرج في تهيئة النظام: {ex.Message}", "خطأ حرج",
                MessageBoxButton.OK, MessageBoxImage.Error);
        }
    }

    private IConfiguration BuildConfiguration()
    {
        var builder = new ConfigurationBuilder()
            .SetBasePath(Directory.GetCurrentDirectory())
            .AddJsonFile("appsettings.json", optional: false, reloadOnChange: true);

        return builder.Build();
    }

    private SupplierDbContext CreateDbContext()
    {
        var connectionString = _configuration.GetConnectionString("DefaultConnection");
        var optionsBuilder = new DbContextOptionsBuilder<SupplierDbContext>();
        optionsBuilder.UseSqlite(connectionString);

        return new SupplierDbContext(optionsBuilder.Options);
    }

    private async void MainWindow_Loaded(object sender, RoutedEventArgs e)
    {
        try
        {
            // إظهار مؤشر التحميل الأولي
            ShowLoading("جاري تهيئة النظام...");

            // التأكد من إنشاء قاعدة البيانات في الخلفية
            await Task.Run(async () =>
            {
                await _context.Database.EnsureCreatedAsync();
                await SeedDatabaseAsync();
            });

            // تحميل البيانات الأساسية فقط
            ShowLoading("جاري تحميل البيانات الأساسية...");
            await LoadDashboardDataAsync();

            // تحميل باقي البيانات في الخلفية
            _ = Task.Run(async () =>
            {
                await Task.Delay(1000); // انتظار ثانية واحدة
                Dispatcher.Invoke(async () => await LoadRecentActivitiesAsync());
            });

            StatusText.Text = "🟢 متصل";
        }
        catch (Exception ex)
        {
            MessageBox.Show($"خطأ في تحميل البيانات: {ex.Message}", "خطأ",
                MessageBoxButton.OK, MessageBoxImage.Error);
            StatusText.Text = "🔴 خطأ في الاتصال";
        }
        finally
        {
            HideLoading();
        }
    }

    private void Timer_Tick(object sender, EventArgs e)
    {
        CurrentTimeText.Text = DateTime.Now.ToString("yyyy/MM/dd HH:mm:ss");
    }

    private async Task LoadDashboardDataAsync()
    {
        try
        {
            // استخدام الإحصائيات المحسنة
            var stats = await _supplierService.GetDashboardStatisticsAsync();

            // تحديث الإحصائيات
            TotalSuppliersText.Text = stats.GetValueOrDefault("total", 0).ToString();
            ApprovedSuppliersText.Text = stats.GetValueOrDefault("approved", 0).ToString();
            PendingSuppliersText.Text = (stats.GetValueOrDefault("pending", 0) + stats.GetValueOrDefault("new", 0)).ToString();
            ExpiringDocumentsText.Text = "0"; // سيتم حسابها لاحقاً عند إضافة نظام الوثائق
        }
        catch (Exception ex)
        {
            MessageBox.Show($"خطأ في تحميل الإحصائيات: {ex.Message}", "خطأ",
                MessageBoxButton.OK, MessageBoxImage.Warning);
        }
    }

    private async Task LoadRecentActivitiesAsync()
    {
        try
        {
            var activities = await _supplierService.GetRecentActivitiesAsync(10);
            RecentActivitiesListView.ItemsSource = activities;
        }
        catch (Exception ex)
        {
            MessageBox.Show($"خطأ في تحميل النشاطات: {ex.Message}", "خطأ",
                MessageBoxButton.OK, MessageBoxImage.Warning);
        }
    }

    // أحداث أزرار القائمة العلوية
    private async void BtnDashboard_Click(object sender, RoutedEventArgs e)
    {
        ShowPanel("Dashboard");
        await LoadDashboardDataAsync();
        await LoadRecentActivitiesAsync();
    }

    private async void BtnSuppliers_Click(object sender, RoutedEventArgs e)
    {
        ShowPanel("Suppliers");
        await ExecuteWithLoadingAsync(() => LoadSuppliersDataAsync(), "جاري تحميل بيانات الموردين...");
    }

    private async void BtnAccreditation_Click(object sender, RoutedEventArgs e)
    {
        ShowPanel("Accreditation");
        await ExecuteWithLoadingAsync(() => LoadAccreditationDataAsync(), "جاري تحميل بيانات الاعتماد...");
    }

    private async void BtnReports_Click(object sender, RoutedEventArgs e)
    {
        ShowPanel("Reports");
        await ExecuteWithLoadingAsync(() => LoadReportsDataAsync(), "جاري تحميل التقارير...");
    }

    private void BtnSettings_Click(object sender, RoutedEventArgs e)
    {
        ShowPanel("Settings");
        // سيتم تنفيذ هذا لاحقاً
    }

    // أحداث الإجراءات السريعة
    private void BtnAddSupplier_Click(object sender, RoutedEventArgs e)
    {
        try
        {
            var addSupplierWindow = new AddSupplierWindow(_supplierService);
            addSupplierWindow.Owner = this;
            var result = addSupplierWindow.ShowDialog();

            if (result == true)
            {
                // تحديث الإحصائيات بعد إضافة مورد جديد
                _ = LoadDashboardDataAsync();
                _ = LoadRecentActivitiesAsync();

                MessageBox.Show("تم إضافة المورد بنجاح وتحديث الإحصائيات!", "نجح الحفظ",
                    MessageBoxButton.OK, MessageBoxImage.Information);
            }
        }
        catch (Exception ex)
        {
            MessageBox.Show($"خطأ في فتح نافذة إضافة المورد: {ex.Message}", "خطأ",
                MessageBoxButton.OK, MessageBoxImage.Error);
        }
    }

    private void BtnSearchSuppliers_Click(object sender, RoutedEventArgs e)
    {
        ShowPanel("Suppliers");
        // سيتم تنفيذ البحث لاحقاً
    }

    private void BtnReviewRequests_Click(object sender, RoutedEventArgs e)
    {
        ShowPanel("Accreditation");
        // سيتم تنفيذ مراجعة الطلبات لاحقاً
    }

    // دالة لإظهار اللوحة المطلوبة
    private void ShowPanel(string panelName)
    {
        // إخفاء جميع اللوحات
        DashboardPanel.Visibility = Visibility.Collapsed;
        SuppliersPanel.Visibility = Visibility.Collapsed;
        AccreditationPanel.Visibility = Visibility.Collapsed;
        ReportsPanel.Visibility = Visibility.Collapsed;
        SettingsPanel.Visibility = Visibility.Collapsed;

        // إظهار اللوحة المطلوبة
        switch (panelName)
        {
            case "Dashboard":
                DashboardPanel.Visibility = Visibility.Visible;
                break;
            case "Suppliers":
                SuppliersPanel.Visibility = Visibility.Visible;
                break;
            case "Accreditation":
                AccreditationPanel.Visibility = Visibility.Visible;
                break;
            case "Reports":
                ReportsPanel.Visibility = Visibility.Visible;
                break;
            case "Settings":
                SettingsPanel.Visibility = Visibility.Visible;
                break;
        }
    }

    protected override void OnClosed(EventArgs e)
    {
        try
        {
            _logger?.LogInfo("بدء إغلاق النظام", "MainWindow");

            // إيقاف المؤقت
            _timer?.Stop();
            _timer?.Dispose();

            // تنظيف قاعدة البيانات
            _context?.Dispose();

            // تنظيف الخدمات
            if (_supplierService is IDisposable disposableService)
            {
                disposableService.Dispose();
            }

            // تنظيف ملفات السجل القديمة
            _logger?.CleanOldLogs(30);

            // تشغيل garbage collection
            GC.Collect();
            GC.WaitForPendingFinalizers();
            GC.Collect();

            _logger?.LogInfo("تم إغلاق النظام بنجاح", "MainWindow");
        }
        catch (Exception ex)
        {
            _logger?.LogError("خطأ في تنظيف الموارد", ex, "MainWindow");
            System.Diagnostics.Debug.WriteLine($"خطأ في تنظيف الموارد: {ex.Message}");
        }
        finally
        {
            base.OnClosed(e);
        }
    }

    // ===============================
    // دوال تحسين الأداء والواجهة
    // ===============================

    private void ShowLoading(string message = "جاري التحميل...")
    {
        Dispatcher.Invoke(() =>
        {
            TxtLoadingMessage.Text = message;
            LoadingIndicator.Visibility = Visibility.Visible;
        });
    }

    private void HideLoading()
    {
        Dispatcher.Invoke(() =>
        {
            LoadingIndicator.Visibility = Visibility.Collapsed;
        });
    }

    private async Task ExecuteWithLoadingAsync(Func<Task> action, string loadingMessage = "جاري التحميل...")
    {
        try
        {
            _logger.LogDebug($"بدء تنفيذ عملية: {loadingMessage}", "MainWindow");
            ShowLoading(loadingMessage);
            await action();
            _logger.LogDebug($"انتهاء تنفيذ عملية: {loadingMessage}", "MainWindow");
        }
        catch (Exception ex)
        {
            _logger.LogError($"خطأ في تنفيذ عملية: {loadingMessage}", ex, "MainWindow");
            MessageBox.Show($"خطأ: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
        }
        finally
        {
            HideLoading();
        }
    }

    // ===============================
    // وظائف صفحة إدارة الموردين
    // ===============================

    private async Task LoadSuppliersDataAsync()
    {
        try
        {
            var suppliers = await _supplierService.GetAllSuppliersAsync();

            // تحديث DataGrid
            SuppliersDataGrid.ItemsSource = suppliers.Select(s => new
            {
                s.SupplierID,
                s.SupplierName,
                s.LicenseNumber,
                s.ContactPerson,
                s.PhoneNumber,
                s.Status,
                StatusColor = GetStatusColor(s.Status),
                s.CreatedDate
            }).ToList();

            // تحديث عداد الموردين
            TxtSuppliersCount.Text = $"إجمالي الموردين: {suppliers.Count()}";

            // تحميل فئات الموردين للفلترة
            await LoadCategoriesForFilterAsync();
        }
        catch (Exception ex)
        {
            MessageBox.Show($"خطأ في تحميل بيانات الموردين: {ex.Message}", "خطأ",
                MessageBoxButton.OK, MessageBoxImage.Error);
        }
    }

    private async Task LoadCategoriesForFilterAsync()
    {
        try
        {
            var categories = await _context.SupplierCategories
                .Where(c => c.IsActive)
                .OrderBy(c => c.CategoryName)
                .ToListAsync();

            CmbCategoryFilter.Items.Clear();
            CmbCategoryFilter.Items.Add(new ComboBoxItem { Content = "جميع الفئات", IsSelected = true });

            foreach (var category in categories)
            {
                CmbCategoryFilter.Items.Add(new ComboBoxItem { Content = category.CategoryName, Tag = category.CategoryID });
            }
        }
        catch (Exception ex)
        {
            MessageBox.Show($"خطأ في تحميل الفئات: {ex.Message}", "خطأ",
                MessageBoxButton.OK, MessageBoxImage.Warning);
        }
    }

    private string GetStatusColor(string status)
    {
        return status switch
        {
            "جديد" => "#3b82f6",      // أزرق
            "قيد المراجعة" => "#f59e0b", // أصفر
            "معتمد" => "#10b981",      // أخضر
            "مرفوض" => "#ef4444",      // أحمر
            "معلق" => "#6b7280",       // رمادي
            _ => "#6b7280"
        };
    }

    // أحداث صفحة الموردين
    private void TxtSearchSuppliers_GotFocus(object sender, RoutedEventArgs e)
    {
        if (TxtSearchSuppliers.Text == "🔍 البحث في الموردين...")
        {
            TxtSearchSuppliers.Text = "";
            TxtSearchSuppliers.Foreground = new SolidColorBrush(Colors.Black);
        }
    }

    private void TxtSearchSuppliers_LostFocus(object sender, RoutedEventArgs e)
    {
        if (string.IsNullOrWhiteSpace(TxtSearchSuppliers.Text))
        {
            TxtSearchSuppliers.Text = "🔍 البحث في الموردين...";
            TxtSearchSuppliers.Foreground = new SolidColorBrush(Colors.Gray);
        }
    }

    private async void TxtSearchSuppliers_TextChanged(object sender, TextChangedEventArgs e)
    {
        if (TxtSearchSuppliers.Text != "🔍 البحث في الموردين..." && !string.IsNullOrWhiteSpace(TxtSearchSuppliers.Text))
        {
            await FilterSuppliersAsync();
        }
        else if (string.IsNullOrWhiteSpace(TxtSearchSuppliers.Text))
        {
            await LoadSuppliersDataAsync();
        }
    }

    private async void CmbStatusFilter_SelectionChanged(object sender, SelectionChangedEventArgs e)
    {
        await FilterSuppliersAsync();
    }

    private async void CmbCategoryFilter_SelectionChanged(object sender, SelectionChangedEventArgs e)
    {
        await FilterSuppliersAsync();
    }

    private async Task FilterSuppliersAsync()
    {
        try
        {
            var suppliers = await _supplierService.GetAllSuppliersAsync();

            // تطبيق فلتر البحث
            if (!string.IsNullOrWhiteSpace(TxtSearchSuppliers.Text) && TxtSearchSuppliers.Text != "🔍 البحث في الموردين...")
            {
                var searchText = TxtSearchSuppliers.Text.ToLower();
                suppliers = suppliers.Where(s =>
                    s.SupplierName.ToLower().Contains(searchText) ||
                    s.LicenseNumber.ToLower().Contains(searchText) ||
                    (s.ContactPerson?.ToLower().Contains(searchText) ?? false)
                ).ToList();
            }

            // تطبيق فلتر الحالة
            if (CmbStatusFilter.SelectedItem is ComboBoxItem statusItem && statusItem.Content.ToString() != "جميع الحالات")
            {
                suppliers = suppliers.Where(s => s.Status == statusItem.Content.ToString()).ToList();
            }

            // تحديث DataGrid
            SuppliersDataGrid.ItemsSource = suppliers.Select(s => new
            {
                s.SupplierID,
                s.SupplierName,
                s.LicenseNumber,
                s.ContactPerson,
                s.PhoneNumber,
                s.Status,
                StatusColor = GetStatusColor(s.Status),
                s.CreatedDate
            }).ToList();

            TxtSuppliersCount.Text = $"إجمالي الموردين: {suppliers.Count()}";
        }
        catch (Exception ex)
        {
            MessageBox.Show($"خطأ في فلترة الموردين: {ex.Message}", "خطأ",
                MessageBoxButton.OK, MessageBoxImage.Error);
        }
    }

    private void BtnAddNewSupplier_Click(object sender, RoutedEventArgs e)
    {
        BtnAddSupplier_Click(sender, e);
    }

    private void SuppliersDataGrid_MouseDoubleClick(object sender, System.Windows.Input.MouseButtonEventArgs e)
    {
        BtnViewSupplier_Click(sender, e);
    }

    private void BtnViewSupplier_Click(object sender, RoutedEventArgs e)
    {
        if (SuppliersDataGrid.SelectedItem != null)
        {
            MessageBox.Show("سيتم فتح نافذة عرض تفاصيل المورد", "عرض المورد",
                MessageBoxButton.OK, MessageBoxImage.Information);
        }
    }

    private void BtnEditSupplier_Click(object sender, RoutedEventArgs e)
    {
        if (SuppliersDataGrid.SelectedItem != null)
        {
            MessageBox.Show("سيتم فتح نافذة تحرير المورد", "تحرير المورد",
                MessageBoxButton.OK, MessageBoxImage.Information);
        }
    }

    private async void BtnDeleteSupplier_Click(object sender, RoutedEventArgs e)
    {
        if (SuppliersDataGrid.SelectedItem != null)
        {
            var result = MessageBox.Show("هل أنت متأكد من حذف هذا المورد؟", "تأكيد الحذف",
                MessageBoxButton.YesNo, MessageBoxImage.Question);

            if (result == MessageBoxResult.Yes)
            {
                try
                {
                    dynamic selectedItem = SuppliersDataGrid.SelectedItem;
                    int supplierId = selectedItem.SupplierID;

                    await _supplierService.DeleteSupplierAsync(supplierId, "المستخدم الحالي");
                    await LoadSuppliersDataAsync();
                    await LoadDashboardDataAsync(); // تحديث الإحصائيات

                    MessageBox.Show("تم حذف المورد بنجاح", "تم الحذف",
                        MessageBoxButton.OK, MessageBoxImage.Information);
                }
                catch (Exception ex)
                {
                    MessageBox.Show($"خطأ في حذف المورد: {ex.Message}", "خطأ",
                        MessageBoxButton.OK, MessageBoxImage.Error);
                }
            }
        }
    }

    // ===============================
    // وظائف صفحة الاعتماد
    // ===============================

    private async Task LoadAccreditationDataAsync()
    {
        try
        {
            var pendingSuppliers = await _supplierService.GetSuppliersByStatusAsync("قيد المراجعة");
            var newSuppliers = await _supplierService.GetSuppliersByStatusAsync("جديد");
            var allPendingSuppliers = pendingSuppliers.Concat(newSuppliers);

            // تحديث الإحصائيات
            TxtPendingCount.Text = allPendingSuppliers.Count().ToString();
            TxtTodayReviews.Text = "0"; // سيتم حسابها من سجل النشاطات
            TxtUrgentReviews.Text = allPendingSuppliers.Where(s =>
                (DateTime.Now - s.CreatedDate).TotalDays > 7).Count().ToString();

            // تحديث DataGrid
            AccreditationDataGrid.ItemsSource = allPendingSuppliers.Select(s => new
            {
                s.SupplierID,
                s.SupplierName,
                s.LicenseNumber,
                s.CreatedDate,
                DocumentCount = 0, // سيتم حسابها من الوثائق
                s.Status,
                StatusColor = GetStatusColor(s.Status)
            }).ToList();
        }
        catch (Exception ex)
        {
            MessageBox.Show($"خطأ في تحميل بيانات الاعتماد: {ex.Message}", "خطأ",
                MessageBoxButton.OK, MessageBoxImage.Error);
        }
    }

    private void BtnRefreshAccreditation_Click(object sender, RoutedEventArgs e)
    {
        _ = LoadAccreditationDataAsync();
    }

    private async void BtnApproveSupplier_Click(object sender, RoutedEventArgs e)
    {
        await UpdateSupplierStatusAsync("معتمد", "تم اعتماد المورد من قبل لجنة المشتريات");
    }

    private async void BtnRejectSupplier_Click(object sender, RoutedEventArgs e)
    {
        await UpdateSupplierStatusAsync("مرفوض", "تم رفض المورد من قبل لجنة المشتريات");
    }

    private async void BtnPostponeSupplier_Click(object sender, RoutedEventArgs e)
    {
        await UpdateSupplierStatusAsync("معلق", "تم تأجيل قرار المورد لمراجعة إضافية");
    }

    private async Task UpdateSupplierStatusAsync(string newStatus, string reason)
    {
        if (AccreditationDataGrid.SelectedItem != null)
        {
            try
            {
                dynamic selectedItem = AccreditationDataGrid.SelectedItem;
                int supplierId = selectedItem.SupplierID;

                var supplier = await _supplierService.GetSupplierByIdAsync(supplierId);
                if (supplier != null)
                {
                    supplier.Status = newStatus;
                    await _supplierService.UpdateSupplierAsync(supplier, "لجنة المشتريات");

                    // إضافة سجل اعتماد
                    var accreditationRecord = new AccreditationRecord
                    {
                        SupplierID = supplierId,
                        ReviewDate = DateTime.Now,
                        Decision = newStatus,
                        ReviewNotes = reason,
                        ReviewedBy = "لجنة المشتريات",
                        IsActive = true
                    };

                    _context.AccreditationRecords.Add(accreditationRecord);
                    await _context.SaveChangesAsync();

                    await LoadAccreditationDataAsync();
                    await LoadDashboardDataAsync(); // تحديث الإحصائيات

                    MessageBox.Show($"تم {newStatus} المورد بنجاح", "تم التحديث",
                        MessageBoxButton.OK, MessageBoxImage.Information);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحديث حالة المورد: {ex.Message}", "خطأ",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }
        else
        {
            MessageBox.Show("يرجى اختيار مورد من القائمة", "تنبيه",
                MessageBoxButton.OK, MessageBoxImage.Warning);
        }
    }

    // ===============================
    // وظائف صفحة التقارير
    // ===============================

    private async Task LoadReportsDataAsync()
    {
        try
        {
            await LoadStatusStatisticsAsync();
            await LoadCategoryStatisticsAsync();
        }
        catch (Exception ex)
        {
            MessageBox.Show($"خطأ في تحميل بيانات التقارير: {ex.Message}", "خطأ",
                MessageBoxButton.OK, MessageBoxImage.Error);
        }
    }

    private async Task LoadStatusStatisticsAsync()
    {
        try
        {
            var suppliers = await _supplierService.GetAllSuppliersAsync();
            var statusGroups = suppliers.GroupBy(s => s.Status).ToList();

            StatusStatisticsPanel.Children.Clear();

            foreach (var group in statusGroups)
            {
                var stackPanel = new StackPanel { Orientation = Orientation.Horizontal, Margin = new Thickness(0, 2, 0, 0) };

                var statusText = new TextBlock
                {
                    Text = $"{group.Key}:",
                    Width = 100,
                    FontWeight = FontWeights.SemiBold
                };

                var countText = new TextBlock
                {
                    Text = group.Count().ToString(),
                    Foreground = new SolidColorBrush((Color)ColorConverter.ConvertFromString(GetStatusColor(group.Key))),
                    FontWeight = FontWeights.Bold
                };

                stackPanel.Children.Add(statusText);
                stackPanel.Children.Add(countText);
                StatusStatisticsPanel.Children.Add(stackPanel);
            }
        }
        catch (Exception ex)
        {
            MessageBox.Show($"خطأ في تحميل إحصائيات الحالات: {ex.Message}", "خطأ",
                MessageBoxButton.OK, MessageBoxImage.Warning);
        }
    }

    private async Task LoadCategoryStatisticsAsync()
    {
        try
        {
            var categories = await _context.SupplierCategories
                .Include(c => c.SupplierMappings)
                .Where(c => c.IsActive)
                .ToListAsync();

            CategoryStatisticsPanel.Children.Clear();

            foreach (var category in categories)
            {
                var stackPanel = new StackPanel { Orientation = Orientation.Horizontal, Margin = new Thickness(0, 2, 0, 0) };

                var categoryText = new TextBlock
                {
                    Text = $"{category.CategoryName}:",
                    Width = 100,
                    FontWeight = FontWeights.SemiBold
                };

                var countText = new TextBlock
                {
                    Text = category.SupplierMappings.Count(m => m.IsActive).ToString(),
                    Foreground = new SolidColorBrush(Colors.DarkBlue),
                    FontWeight = FontWeights.Bold
                };

                stackPanel.Children.Add(categoryText);
                stackPanel.Children.Add(countText);
                CategoryStatisticsPanel.Children.Add(stackPanel);
            }
        }
        catch (Exception ex)
        {
            MessageBox.Show($"خطأ في تحميل إحصائيات الفئات: {ex.Message}", "خطأ",
                MessageBoxButton.OK, MessageBoxImage.Warning);
        }
    }

    // أحداث التقارير
    private async void BtnSuppliersStatusReport_Click(object sender, RoutedEventArgs e)
    {
        await GenerateStatusReportAsync();
    }

    private async void BtnExpiringDocumentsReport_Click(object sender, RoutedEventArgs e)
    {
        await GenerateExpiringDocumentsReportAsync();
    }

    private async void BtnActivitiesReport_Click(object sender, RoutedEventArgs e)
    {
        await GenerateActivitiesReportAsync();
    }

    private async void BtnCategoriesReport_Click(object sender, RoutedEventArgs e)
    {
        await GenerateCategoriesReportAsync();
    }

    private async Task GenerateStatusReportAsync()
    {
        try
        {
            TxtReportTitle.Text = "📊 تقرير حالة الموردين";
            ReportContentPanel.Children.Clear();

            var suppliers = await _supplierService.GetAllSuppliersAsync();
            var statusGroups = suppliers.GroupBy(s => s.Status).ToList();

            var reportText = new TextBlock
            {
                Text = $"تقرير حالة الموردين - تاريخ الإنشاء: {DateTime.Now:yyyy/MM/dd HH:mm}\n\n",
                FontSize = 16,
                FontWeight = FontWeights.Bold,
                Margin = new Thickness(10, 10, 10, 10)
            };
            ReportContentPanel.Children.Add(reportText);

            foreach (var group in statusGroups)
            {
                var statusPanel = new StackPanel { Margin = new Thickness(10, 5, 10, 5) };

                var statusHeader = new TextBlock
                {
                    Text = $"{group.Key}: {group.Count()} مورد",
                    FontSize = 14,
                    FontWeight = FontWeights.SemiBold,
                    Foreground = new SolidColorBrush((Color)ColorConverter.ConvertFromString(GetStatusColor(group.Key)))
                };
                statusPanel.Children.Add(statusHeader);

                foreach (var supplier in group.Take(10)) // عرض أول 10 موردين فقط
                {
                    var supplierText = new TextBlock
                    {
                        Text = $"  • {supplier.SupplierName} - {supplier.LicenseNumber}",
                        FontSize = 12,
                        Margin = new Thickness(20, 2, 20, 2)
                    };
                    statusPanel.Children.Add(supplierText);
                }

                if (group.Count() > 10)
                {
                    var moreText = new TextBlock
                    {
                        Text = $"  ... و {group.Count() - 10} مورد آخر",
                        FontSize = 12,
                        FontStyle = FontStyles.Italic,
                        Margin = new Thickness(20, 2, 20, 2)
                    };
                    statusPanel.Children.Add(moreText);
                }

                ReportContentPanel.Children.Add(statusPanel);
            }
        }
        catch (Exception ex)
        {
            MessageBox.Show($"خطأ في إنشاء تقرير الحالات: {ex.Message}", "خطأ",
                MessageBoxButton.OK, MessageBoxImage.Error);
        }
    }

    private async Task GenerateExpiringDocumentsReportAsync()
    {
        try
        {
            TxtReportTitle.Text = "⚠️ تقرير الوثائق المنتهية";
            ReportContentPanel.Children.Clear();

            var reportText = new TextBlock
            {
                Text = $"تقرير الوثائق المنتهية والتي تنتهي قريباً - تاريخ الإنشاء: {DateTime.Now:yyyy/MM/dd HH:mm}\n\n",
                FontSize = 16,
                FontWeight = FontWeights.Bold,
                Margin = new Thickness(10, 10, 10, 10)
            };
            ReportContentPanel.Children.Add(reportText);

            var warningText = new TextBlock
            {
                Text = "هذا التقرير سيتم تطويره عند إضافة نظام إدارة الوثائق",
                FontSize = 14,
                FontStyle = FontStyles.Italic,
                Foreground = new SolidColorBrush(Colors.Orange),
                Margin = new Thickness(10, 10, 10, 10)
            };
            ReportContentPanel.Children.Add(warningText);
        }
        catch (Exception ex)
        {
            MessageBox.Show($"خطأ في إنشاء تقرير الوثائق: {ex.Message}", "خطأ",
                MessageBoxButton.OK, MessageBoxImage.Error);
        }
    }

    private async Task GenerateActivitiesReportAsync()
    {
        try
        {
            TxtReportTitle.Text = "📝 تقرير النشاطات";
            ReportContentPanel.Children.Clear();

            var activities = await _context.ActivityLogs
                .OrderByDescending(a => a.PerformedDate)
                .Take(50)
                .ToListAsync();

            var reportText = new TextBlock
            {
                Text = $"تقرير آخر النشاطات (آخر 50 نشاط) - تاريخ الإنشاء: {DateTime.Now:yyyy/MM/dd HH:mm}\n\n",
                FontSize = 16,
                FontWeight = FontWeights.Bold,
                Margin = new Thickness(10, 10, 10, 10)
            };
            ReportContentPanel.Children.Add(reportText);

            foreach (var activity in activities)
            {
                var activityPanel = new StackPanel { Margin = new Thickness(10, 5, 10, 5) };

                var activityText = new TextBlock
                {
                    Text = $"{activity.PerformedDate:yyyy/MM/dd HH:mm} - {activity.ActivityDescription}",
                    FontSize = 12,
                    Margin = new Thickness(0, 2, 0, 2)
                };

                var performerText = new TextBlock
                {
                    Text = $"بواسطة: {activity.PerformedBy}",
                    FontSize = 10,
                    Foreground = new SolidColorBrush(Colors.Gray),
                    Margin = new Thickness(20, 0, 20, 0)
                };

                activityPanel.Children.Add(activityText);
                activityPanel.Children.Add(performerText);
                ReportContentPanel.Children.Add(activityPanel);
            }
        }
        catch (Exception ex)
        {
            MessageBox.Show($"خطأ في إنشاء تقرير النشاطات: {ex.Message}", "خطأ",
                MessageBoxButton.OK, MessageBoxImage.Error);
        }
    }

    private async Task GenerateCategoriesReportAsync()
    {
        try
        {
            TxtReportTitle.Text = "🏷️ تقرير الفئات";
            ReportContentPanel.Children.Clear();

            var categories = await _context.SupplierCategories
                .Include(c => c.SupplierMappings)
                .ThenInclude(m => m.Supplier)
                .Where(c => c.IsActive)
                .ToListAsync();

            var reportText = new TextBlock
            {
                Text = $"تقرير توزيع الموردين حسب الفئات - تاريخ الإنشاء: {DateTime.Now:yyyy/MM/dd HH:mm}\n\n",
                FontSize = 16,
                FontWeight = FontWeights.Bold,
                Margin = new Thickness(10, 10, 10, 10)
            };
            ReportContentPanel.Children.Add(reportText);

            foreach (var category in categories)
            {
                var categoryPanel = new StackPanel { Margin = new Thickness(10, 5, 10, 5) };

                var categoryHeader = new TextBlock
                {
                    Text = $"{category.CategoryName}: {category.SupplierMappings.Count(m => m.IsActive)} مورد",
                    FontSize = 14,
                    FontWeight = FontWeights.SemiBold,
                    Foreground = new SolidColorBrush(Colors.DarkBlue)
                };
                categoryPanel.Children.Add(categoryHeader);

                var activeSuppliers = category.SupplierMappings
                    .Where(m => m.IsActive && m.Supplier.IsActive)
                    .Take(10);

                foreach (var mapping in activeSuppliers)
                {
                    var supplierText = new TextBlock
                    {
                        Text = $"  • {mapping.Supplier.SupplierName} - {mapping.Supplier.LicenseNumber}",
                        FontSize = 12,
                        Margin = new Thickness(20, 2, 20, 2)
                    };
                    categoryPanel.Children.Add(supplierText);
                }

                if (category.SupplierMappings.Count(m => m.IsActive) > 10)
                {
                    var moreText = new TextBlock
                    {
                        Text = $"  ... و {category.SupplierMappings.Count(m => m.IsActive) - 10} مورد آخر",
                        FontSize = 12,
                        FontStyle = FontStyles.Italic,
                        Margin = new Thickness(20, 2, 20, 2)
                    };
                    categoryPanel.Children.Add(moreText);
                }

                ReportContentPanel.Children.Add(categoryPanel);
            }
        }
        catch (Exception ex)
        {
            MessageBox.Show($"خطأ في إنشاء تقرير الفئات: {ex.Message}", "خطأ",
                MessageBoxButton.OK, MessageBoxImage.Error);
        }
    }

    private void BtnPrintReport_Click(object sender, RoutedEventArgs e)
    {
        try
        {
            var printDialog = new PrintDialog();
            if (printDialog.ShowDialog() == true)
            {
                printDialog.PrintVisual(ReportContentScrollViewer, "تقرير نظام إدارة الموردين");
            }
        }
        catch (Exception ex)
        {
            MessageBox.Show($"خطأ في طباعة التقرير: {ex.Message}", "خطأ",
                MessageBoxButton.OK, MessageBoxImage.Error);
        }
    }

    private void BtnExportReport_Click(object sender, RoutedEventArgs e)
    {
        MessageBox.Show("سيتم تطوير ميزة التصدير قريباً", "قيد التطوير",
            MessageBoxButton.OK, MessageBoxImage.Information);
    }

    // ===============================
    // وظائف صفحة الإعدادات
    // ===============================

    private void LoadSettingsData()
    {
        try
        {
            // تحميل الإعدادات من ملف التكوين
            TxtApplicationName.Text = _configuration["ApplicationSettings:ApplicationName"] ?? "نظام إدارة الموردين - جمعية المنقف التعاونية";
            TxtCompanyName.Text = _configuration["ApplicationSettings:CompanyName"] ?? "جمعية المنقف التعاونية";
            TxtDocumentWarningDays.Text = _configuration["ApplicationSettings:DocumentWarningDays"] ?? "30";

            // تحديث حجم قاعدة البيانات
            UpdateDatabaseSize();
        }
        catch (Exception ex)
        {
            MessageBox.Show($"خطأ في تحميل الإعدادات: {ex.Message}", "خطأ",
                MessageBoxButton.OK, MessageBoxImage.Warning);
        }
    }

    private void UpdateDatabaseSize()
    {
        try
        {
            var dbPath = TxtDatabasePath.Text;
            if (File.Exists(dbPath))
            {
                var fileInfo = new FileInfo(dbPath);
                var sizeInMB = fileInfo.Length / (1024.0 * 1024.0);
                TxtDatabaseSize.Text = $"{sizeInMB:F2} ميجابايت";
            }
            else
            {
                TxtDatabaseSize.Text = "قاعدة البيانات غير موجودة";
            }
        }
        catch (Exception ex)
        {
            TxtDatabaseSize.Text = $"خطأ في حساب الحجم: {ex.Message}";
        }
    }

    private void BtnBrowseDatabasePath_Click(object sender, RoutedEventArgs e)
    {
        var openFileDialog = new Microsoft.Win32.OpenFileDialog
        {
            Filter = "SQLite Database Files (*.db)|*.db|All Files (*.*)|*.*",
            Title = "اختر ملف قاعدة البيانات"
        };

        if (openFileDialog.ShowDialog() == true)
        {
            TxtDatabasePath.Text = openFileDialog.FileName;
            UpdateDatabaseSize();
        }
    }

    private async void BtnBackupDatabase_Click(object sender, RoutedEventArgs e)
    {
        try
        {
            var saveFileDialog = new Microsoft.Win32.SaveFileDialog
            {
                Filter = "SQLite Database Files (*.db)|*.db",
                Title = "حفظ النسخة الاحتياطية",
                FileName = $"SupplierManagement_Backup_{DateTime.Now:yyyyMMdd_HHmmss}.db"
            };

            if (saveFileDialog.ShowDialog() == true)
            {
                var sourcePath = TxtDatabasePath.Text;
                var destinationPath = saveFileDialog.FileName;

                if (File.Exists(sourcePath))
                {
                    File.Copy(sourcePath, destinationPath, true);
                    MessageBox.Show("تم إنشاء النسخة الاحتياطية بنجاح", "تم الحفظ",
                        MessageBoxButton.OK, MessageBoxImage.Information);
                }
                else
                {
                    MessageBox.Show("ملف قاعدة البيانات غير موجود", "خطأ",
                        MessageBoxButton.OK, MessageBoxImage.Error);
                }
            }
        }
        catch (Exception ex)
        {
            MessageBox.Show($"خطأ في إنشاء النسخة الاحتياطية: {ex.Message}", "خطأ",
                MessageBoxButton.OK, MessageBoxImage.Error);
        }
    }

    private async void BtnRestoreDatabase_Click(object sender, RoutedEventArgs e)
    {
        try
        {
            var result = MessageBox.Show("هل أنت متأكد من استعادة النسخة الاحتياطية؟ سيتم استبدال قاعدة البيانات الحالية.",
                "تأكيد الاستعادة", MessageBoxButton.YesNo, MessageBoxImage.Question);

            if (result == MessageBoxResult.Yes)
            {
                var openFileDialog = new Microsoft.Win32.OpenFileDialog
                {
                    Filter = "SQLite Database Files (*.db)|*.db",
                    Title = "اختر ملف النسخة الاحتياطية"
                };

                if (openFileDialog.ShowDialog() == true)
                {
                    var sourcePath = openFileDialog.FileName;
                    var destinationPath = TxtDatabasePath.Text;

                    // إغلاق الاتصال الحالي
                    _context?.Dispose();

                    File.Copy(sourcePath, destinationPath, true);

                    MessageBox.Show("تم استعادة النسخة الاحتياطية بنجاح. يرجى إعادة تشغيل التطبيق لتطبيق التغييرات.", "تم الاستعادة",
                        MessageBoxButton.OK, MessageBoxImage.Information);
                }
            }
        }
        catch (Exception ex)
        {
            MessageBox.Show($"خطأ في استعادة النسخة الاحتياطية: {ex.Message}", "خطأ",
                MessageBoxButton.OK, MessageBoxImage.Error);
        }
    }

    private async void BtnOptimizeDatabase_Click(object sender, RoutedEventArgs e)
    {
        try
        {
            await _context.Database.ExecuteSqlRawAsync("VACUUM;");
            UpdateDatabaseSize();
            MessageBox.Show("تم تحسين قاعدة البيانات بنجاح", "تم التحسين",
                MessageBoxButton.OK, MessageBoxImage.Information);
        }
        catch (Exception ex)
        {
            MessageBox.Show($"خطأ في تحسين قاعدة البيانات: {ex.Message}", "خطأ",
                MessageBoxButton.OK, MessageBoxImage.Error);
        }
    }

    private void BtnSaveSettings_Click(object sender, RoutedEventArgs e)
    {
        try
        {
            // في تطبيق حقيقي، سيتم حفظ الإعدادات في ملف التكوين
            MessageBox.Show("تم حفظ الإعدادات بنجاح", "تم الحفظ",
                MessageBoxButton.OK, MessageBoxImage.Information);
        }
        catch (Exception ex)
        {
            MessageBox.Show($"خطأ في حفظ الإعدادات: {ex.Message}", "خطأ",
                MessageBoxButton.OK, MessageBoxImage.Error);
        }
    }

    private void BtnResetSettings_Click(object sender, RoutedEventArgs e)
    {
        var result = MessageBox.Show("هل أنت متأكد من إعادة تعيين الإعدادات إلى القيم الافتراضية؟",
            "تأكيد إعادة التعيين", MessageBoxButton.YesNo, MessageBoxImage.Question);

        if (result == MessageBoxResult.Yes)
        {
            TxtApplicationName.Text = "نظام إدارة الموردين - جمعية المنقف التعاونية";
            TxtCompanyName.Text = "جمعية المنقف التعاونية";
            TxtDocumentWarningDays.Text = "30";
            ChkAutoBackup.IsChecked = true;
            ChkShowNotifications.IsChecked = true;

            MessageBox.Show("تم إعادة تعيين الإعدادات بنجاح", "تم إعادة التعيين",
                MessageBoxButton.OK, MessageBoxImage.Information);
        }
    }

    // ===============================
    // إنشاء البيانات التجريبية
    // ===============================

    private async Task SeedDatabaseAsync()
    {
        try
        {
            // التحقق من وجود بيانات
            var existingSuppliers = await _context.Suppliers.AnyAsync();
            if (existingSuppliers) return; // البيانات موجودة مسبقاً

            // إنشاء فئات الموردين
            var categories = new List<SupplierCategory>
            {
                new SupplierCategory { CategoryName = "مواد غذائية", Description = "موردو المواد الغذائية والمشروبات", IsActive = true },
                new SupplierCategory { CategoryName = "مواد تنظيف", Description = "موردو مواد التنظيف والصيانة", IsActive = true },
                new SupplierCategory { CategoryName = "أجهزة كهربائية", Description = "موردو الأجهزة الكهربائية والإلكترونية", IsActive = true },
                new SupplierCategory { CategoryName = "مستلزمات مكتبية", Description = "موردو المستلزمات المكتبية والقرطاسية", IsActive = true },
                new SupplierCategory { CategoryName = "خدمات عامة", Description = "مقدمو الخدمات العامة والصيانة", IsActive = true }
            };

            _context.SupplierCategories.AddRange(categories);
            await _context.SaveChangesAsync();

            // إنشاء أنواع الوثائق
            var documentTypes = new List<DocumentType>
            {
                new DocumentType { TypeName = "الرخصة التجارية", Description = "رخصة مزاولة النشاط التجاري", ValidityPeriodMonths = 12, IsRequired = true, IsActive = true },
                new DocumentType { TypeName = "شهادة الغرفة التجارية", Description = "شهادة عضوية الغرفة التجارية", ValidityPeriodMonths = 12, IsRequired = true, IsActive = true },
                new DocumentType { TypeName = "شهادة الجودة", Description = "شهادة ضمان الجودة والمطابقة", ValidityPeriodMonths = 24, IsRequired = false, IsActive = true },
                new DocumentType { TypeName = "بوليصة التأمين", Description = "بوليصة تأمين المسؤولية المدنية", ValidityPeriodMonths = 12, IsRequired = false, IsActive = true },
                new DocumentType { TypeName = "السجل الضريبي", Description = "شهادة السجل الضريبي", ValidityPeriodMonths = 12, IsRequired = true, IsActive = true }
            };

            _context.DocumentTypes.AddRange(documentTypes);
            await _context.SaveChangesAsync();

            // إنشاء أعضاء لجنة المشتريات
            var committeeMembers = new List<ProcurementCommitteeMember>
            {
                new ProcurementCommitteeMember { MemberName = "أحمد محمد الكندري", Position = "رئيس اللجنة", IsChairman = true, IsActive = true, JoinDate = DateTime.Now.AddMonths(-6) },
                new ProcurementCommitteeMember { MemberName = "فاطمة علي العتيبي", Position = "نائب الرئيس", IsChairman = false, IsActive = true, JoinDate = DateTime.Now.AddMonths(-6) },
                new ProcurementCommitteeMember { MemberName = "خالد سعد المطيري", Position = "عضو", IsChairman = false, IsActive = true, JoinDate = DateTime.Now.AddMonths(-4) },
                new ProcurementCommitteeMember { MemberName = "نورا عبدالله الرشيد", Position = "عضو", IsChairman = false, IsActive = true, JoinDate = DateTime.Now.AddMonths(-3) }
            };

            _context.ProcurementCommittee.AddRange(committeeMembers);
            await _context.SaveChangesAsync();

            // إنشاء موردين تجريبيين
            var suppliers = new List<Supplier>
            {
                new Supplier
                {
                    SupplierName = "شركة الخليج للمواد الغذائية",
                    CommercialName = "Gulf Food Company",
                    LicenseNumber = "CR001234567",
                    ContactPerson = "محمد أحمد السالم",
                    PhoneNumber = "+965 2398 1234",
                    Email = "<EMAIL>",
                    Address = "الشويخ الصناعية، قطعة 5، شارع 15",
                    Status = "معتمد",
                    CreatedBy = "النظام",
                    ModifiedBy = "النظام",
                    CreatedDate = DateTime.Now.AddDays(-30),
                    ModifiedDate = DateTime.Now.AddDays(-15),
                    IsActive = true
                },
                new Supplier
                {
                    SupplierName = "مؤسسة النظافة الحديثة",
                    CommercialName = "Modern Cleaning Est.",
                    LicenseNumber = "CR002345678",
                    ContactPerson = "سارة خالد المنصور",
                    PhoneNumber = "+965 2398 5678",
                    Email = "<EMAIL>",
                    Address = "الفروانية، قطعة 12، شارع 8",
                    Status = "قيد المراجعة",
                    CreatedBy = "النظام",
                    ModifiedBy = "النظام",
                    CreatedDate = DateTime.Now.AddDays(-10),
                    ModifiedDate = DateTime.Now.AddDays(-10),
                    IsActive = true
                },
                new Supplier
                {
                    SupplierName = "شركة التقنية المتطورة",
                    CommercialName = "Advanced Tech Co.",
                    LicenseNumber = "CR003456789",
                    ContactPerson = "عبدالعزيز فهد الدوسري",
                    PhoneNumber = "+965 2398 9012",
                    Email = "<EMAIL>",
                    Address = "حولي، شارع تونس، مجمع الأعمال",
                    Status = "جديد",
                    CreatedBy = "النظام",
                    ModifiedBy = "النظام",
                    CreatedDate = DateTime.Now.AddDays(-5),
                    ModifiedDate = DateTime.Now.AddDays(-5),
                    IsActive = true
                },
                new Supplier
                {
                    SupplierName = "مكتبة الرواد للقرطاسية",
                    CommercialName = "Pioneers Stationery",
                    LicenseNumber = "CR004567890",
                    ContactPerson = "هند عبدالله العجمي",
                    PhoneNumber = "+965 2398 3456",
                    Email = "<EMAIL>",
                    Address = "السالمية، شارع الخليج العربي",
                    Status = "معتمد",
                    CreatedBy = "النظام",
                    ModifiedBy = "النظام",
                    CreatedDate = DateTime.Now.AddDays(-45),
                    ModifiedDate = DateTime.Now.AddDays(-20),
                    IsActive = true
                },
                new Supplier
                {
                    SupplierName = "شركة الخدمات الشاملة",
                    CommercialName = "Comprehensive Services Co.",
                    LicenseNumber = "CR005678901",
                    ContactPerson = "يوسف محمد البلوشي",
                    PhoneNumber = "+965 2398 7890",
                    Email = "<EMAIL>",
                    Address = "الجهراء، المنطقة الصناعية الجديدة",
                    Status = "معلق",
                    CreatedBy = "النظام",
                    ModifiedBy = "النظام",
                    CreatedDate = DateTime.Now.AddDays(-20),
                    ModifiedDate = DateTime.Now.AddDays(-8),
                    IsActive = true
                }
            };

            _context.Suppliers.AddRange(suppliers);
            await _context.SaveChangesAsync();

            // ربط الموردين بالفئات
            var categoryMappings = new List<SupplierCategoryMapping>
            {
                new SupplierCategoryMapping { SupplierID = suppliers[0].SupplierID, CategoryID = categories[0].CategoryID, AssignedBy = "النظام", AssignedDate = DateTime.Now, IsActive = true },
                new SupplierCategoryMapping { SupplierID = suppliers[1].SupplierID, CategoryID = categories[1].CategoryID, AssignedBy = "النظام", AssignedDate = DateTime.Now, IsActive = true },
                new SupplierCategoryMapping { SupplierID = suppliers[2].SupplierID, CategoryID = categories[2].CategoryID, AssignedBy = "النظام", AssignedDate = DateTime.Now, IsActive = true },
                new SupplierCategoryMapping { SupplierID = suppliers[3].SupplierID, CategoryID = categories[3].CategoryID, AssignedBy = "النظام", AssignedDate = DateTime.Now, IsActive = true },
                new SupplierCategoryMapping { SupplierID = suppliers[4].SupplierID, CategoryID = categories[4].CategoryID, AssignedBy = "النظام", AssignedDate = DateTime.Now, IsActive = true }
            };

            _context.SupplierCategoryMappings.AddRange(categoryMappings);
            await _context.SaveChangesAsync();

            // إضافة سجلات نشاط
            var activities = new List<ActivityLog>
            {
                new ActivityLog { SupplierID = suppliers[0].SupplierID, ActivityType = ActivityType.Create, ActivityDescription = "تم إنشاء مورد جديد: شركة الخليج للمواد الغذائية", PerformedBy = "النظام", PerformedDate = DateTime.Now.AddDays(-30) },
                new ActivityLog { SupplierID = suppliers[0].SupplierID, ActivityType = ActivityType.Update, ActivityDescription = "تم اعتماد المورد: شركة الخليج للمواد الغذائية", PerformedBy = "أحمد محمد الكندري", PerformedDate = DateTime.Now.AddDays(-15) },
                new ActivityLog { SupplierID = suppliers[1].SupplierID, ActivityType = ActivityType.Create, ActivityDescription = "تم إنشاء مورد جديد: مؤسسة النظافة الحديثة", PerformedBy = "النظام", PerformedDate = DateTime.Now.AddDays(-10) },
                new ActivityLog { SupplierID = suppliers[2].SupplierID, ActivityType = ActivityType.Create, ActivityDescription = "تم إنشاء مورد جديد: شركة التقنية المتطورة", PerformedBy = "النظام", PerformedDate = DateTime.Now.AddDays(-5) },
                new ActivityLog { SupplierID = suppliers[3].SupplierID, ActivityType = ActivityType.Create, ActivityDescription = "تم إنشاء مورد جديد: مكتبة الرواد للقرطاسية", PerformedBy = "النظام", PerformedDate = DateTime.Now.AddDays(-45) },
                new ActivityLog { SupplierID = suppliers[3].SupplierID, ActivityType = ActivityType.Update, ActivityDescription = "تم اعتماد المورد: مكتبة الرواد للقرطاسية", PerformedBy = "فاطمة علي العتيبي", PerformedDate = DateTime.Now.AddDays(-20) },
                new ActivityLog { SupplierID = suppliers[4].SupplierID, ActivityType = ActivityType.Create, ActivityDescription = "تم إنشاء مورد جديد: شركة الخدمات الشاملة", PerformedBy = "النظام", PerformedDate = DateTime.Now.AddDays(-20) },
                new ActivityLog { SupplierID = suppliers[4].SupplierID, ActivityType = ActivityType.Update, ActivityDescription = "تم تعليق المورد: شركة الخدمات الشاملة لمراجعة إضافية", PerformedBy = "خالد سعد المطيري", PerformedDate = DateTime.Now.AddDays(-8) }
            };

            _context.ActivityLogs.AddRange(activities);
            await _context.SaveChangesAsync();

            // إضافة سجلات اعتماد
            var accreditationRecords = new List<AccreditationRecord>
            {
                new AccreditationRecord { SupplierID = suppliers[0].SupplierID, ReviewDate = DateTime.Now.AddDays(-15), Decision = "معتمد", ReviewNotes = "تم اعتماد المورد بعد مراجعة جميع الوثائق", ReviewedBy = "أحمد محمد الكندري", IsActive = true },
                new AccreditationRecord { SupplierID = suppliers[3].SupplierID, ReviewDate = DateTime.Now.AddDays(-20), Decision = "معتمد", ReviewNotes = "مورد موثوق مع سجل جيد", ReviewedBy = "فاطمة علي العتيبي", IsActive = true },
                new AccreditationRecord { SupplierID = suppliers[4].SupplierID, ReviewDate = DateTime.Now.AddDays(-8), Decision = "معلق", ReviewNotes = "يحتاج إلى وثائق إضافية للمراجعة", ReviewedBy = "خالد سعد المطيري", IsActive = true }
            };

            _context.AccreditationRecords.AddRange(accreditationRecords);
            await _context.SaveChangesAsync();

        }
        catch (Exception ex)
        {
            // في حالة الخطأ، لا نعرض رسالة للمستخدم لأن هذه بيانات تجريبية
            System.Diagnostics.Debug.WriteLine($"خطأ في إنشاء البيانات التجريبية: {ex.Message}");
        }
    }
}