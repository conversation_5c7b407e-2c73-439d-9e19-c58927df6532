using Microsoft.EntityFrameworkCore;
using SupplierManagementSystem.Models;
using System;

namespace SupplierManagementSystem.Data
{
    /// <summary>
    /// سياق قاعدة البيانات لنظام إدارة الموردين
    /// Database Context for Supplier Management System
    /// </summary>
    public class SupplierDbContext : DbContext
    {
        public SupplierDbContext(DbContextOptions<SupplierDbContext> options) : base(options)
        {
        }

        // DbSets - مجموعات البيانات
        public DbSet<Supplier> Suppliers { get; set; }
        public DbSet<DocumentType> DocumentTypes { get; set; }
        public DbSet<SupplierDocument> SupplierDocuments { get; set; }
        public DbSet<ProcurementCommitteeMember> ProcurementCommittee { get; set; }
        public DbSet<AccreditationRecord> AccreditationRecords { get; set; }
        public DbSet<SupplierEvaluation> SupplierEvaluations { get; set; }
        public DbSet<SupplierCategory> SupplierCategories { get; set; }
        public DbSet<SupplierCategoryMapping> SupplierCategoryMappings { get; set; }
        public DbSet<ActivityLog> ActivityLogs { get; set; }

        protected override void OnModelCreating(ModelBuilder modelBuilder)
        {
            base.OnModelCreating(modelBuilder);

            // تكوين الجداول والعلاقات
            ConfigureSupplier(modelBuilder);
            ConfigureDocuments(modelBuilder);
            ConfigureAccreditation(modelBuilder);
            ConfigureEvaluation(modelBuilder);
            ConfigureCategories(modelBuilder);
            ConfigureActivityLog(modelBuilder);

            // إدراج البيانات الأولية
            SeedData(modelBuilder);
        }

        private void ConfigureSupplier(ModelBuilder modelBuilder)
        {
            modelBuilder.Entity<Supplier>(entity =>
            {
                entity.HasKey(e => e.SupplierID);
                entity.Property(e => e.SupplierName).IsRequired().HasMaxLength(200);
                entity.Property(e => e.LicenseNumber).IsRequired().HasMaxLength(50);
                entity.HasIndex(e => e.LicenseNumber).IsUnique();
                entity.Property(e => e.Status).HasDefaultValue("جديد");
                entity.Property(e => e.CreatedDate).HasDefaultValueSql("datetime('now')");
                entity.Property(e => e.ModifiedDate).HasDefaultValueSql("datetime('now')");
                entity.Property(e => e.IsActive).HasDefaultValue(true);
            });
        }

        private void ConfigureDocuments(ModelBuilder modelBuilder)
        {
            modelBuilder.Entity<DocumentType>(entity =>
            {
                entity.HasKey(e => e.DocumentTypeID);
                entity.Property(e => e.TypeName).IsRequired().HasMaxLength(100);
                entity.Property(e => e.ValidityPeriodMonths).HasDefaultValue(12);
                entity.Property(e => e.IsActive).HasDefaultValue(true);
            });

            modelBuilder.Entity<SupplierDocument>(entity =>
            {
                entity.HasKey(e => e.DocumentID);
                entity.Property(e => e.DocumentName).IsRequired().HasMaxLength(200);
                entity.Property(e => e.Status).HasDefaultValue("سارية");
                entity.Property(e => e.UploadedDate).HasDefaultValueSql("datetime('now')");
                entity.Property(e => e.IsActive).HasDefaultValue(true);

                entity.HasOne(d => d.Supplier)
                    .WithMany(p => p.Documents)
                    .HasForeignKey(d => d.SupplierID)
                    .OnDelete(DeleteBehavior.Cascade);

                entity.HasOne(d => d.DocumentType)
                    .WithMany(p => p.SupplierDocuments)
                    .HasForeignKey(d => d.DocumentTypeID);
            });
        }

        private void ConfigureAccreditation(ModelBuilder modelBuilder)
        {
            modelBuilder.Entity<ProcurementCommitteeMember>(entity =>
            {
                entity.HasKey(e => e.CommitteeID);
                entity.Property(e => e.MemberName).IsRequired().HasMaxLength(100);
                entity.Property(e => e.IsChairman).HasDefaultValue(false);
                entity.Property(e => e.IsActive).HasDefaultValue(true);
                entity.Property(e => e.JoinDate).HasDefaultValueSql("date('now')");
            });

            modelBuilder.Entity<AccreditationRecord>(entity =>
            {
                entity.HasKey(e => e.RecordID);
                entity.Property(e => e.ReviewDate).HasDefaultValueSql("datetime('now')");
                entity.Property(e => e.IsActive).HasDefaultValue(true);

                entity.HasOne(d => d.Supplier)
                    .WithMany(p => p.AccreditationRecords)
                    .HasForeignKey(d => d.SupplierID)
                    .OnDelete(DeleteBehavior.Cascade);

                entity.HasOne(d => d.Reviewer)
                    .WithMany(p => p.AccreditationRecords)
                    .HasForeignKey(d => d.ReviewerID);
            });
        }

        private void ConfigureEvaluation(ModelBuilder modelBuilder)
        {
            modelBuilder.Entity<SupplierEvaluation>(entity =>
            {
                entity.HasKey(e => e.EvaluationID);
                entity.Property(e => e.EvaluationDate).HasDefaultValueSql("datetime('now')");
                entity.Property(e => e.QualityScore).HasColumnType("decimal(3,2)");
                entity.Property(e => e.DeliveryScore).HasColumnType("decimal(3,2)");
                entity.Property(e => e.ServiceScore).HasColumnType("decimal(3,2)");
                entity.Property(e => e.PriceScore).HasColumnType("decimal(3,2)");
                entity.Property(e => e.IsActive).HasDefaultValue(true);

                entity.HasOne(d => d.Supplier)
                    .WithMany(p => p.Evaluations)
                    .HasForeignKey(d => d.SupplierID)
                    .OnDelete(DeleteBehavior.Cascade);

                entity.HasOne(d => d.Evaluator)
                    .WithMany(p => p.SupplierEvaluations)
                    .HasForeignKey(d => d.EvaluatorID);
            });
        }

        private void ConfigureCategories(ModelBuilder modelBuilder)
        {
            modelBuilder.Entity<SupplierCategory>(entity =>
            {
                entity.HasKey(e => e.CategoryID);
                entity.Property(e => e.CategoryName).IsRequired().HasMaxLength(100);
                entity.Property(e => e.IsActive).HasDefaultValue(true);
            });

            modelBuilder.Entity<SupplierCategoryMapping>(entity =>
            {
                entity.HasKey(e => e.MappingID);
                entity.Property(e => e.AssignedDate).HasDefaultValueSql("datetime('now')");
                entity.Property(e => e.IsActive).HasDefaultValue(true);

                entity.HasOne(d => d.Supplier)
                    .WithMany(p => p.CategoryMappings)
                    .HasForeignKey(d => d.SupplierID)
                    .OnDelete(DeleteBehavior.Cascade);

                entity.HasOne(d => d.Category)
                    .WithMany(p => p.SupplierMappings)
                    .HasForeignKey(d => d.CategoryID);
            });
        }

        private void ConfigureActivityLog(ModelBuilder modelBuilder)
        {
            modelBuilder.Entity<ActivityLog>(entity =>
            {
                entity.HasKey(e => e.LogID);
                entity.Property(e => e.ActivityType).IsRequired().HasMaxLength(50);
                entity.Property(e => e.ActivityDescription).IsRequired().HasMaxLength(500);
                entity.Property(e => e.PerformedBy).IsRequired().HasMaxLength(100);
                entity.Property(e => e.PerformedDate).HasDefaultValueSql("datetime('now')");

                entity.HasOne(d => d.Supplier)
                    .WithMany(p => p.ActivityLogs)
                    .HasForeignKey(d => d.SupplierID);
            });
        }

        private void SeedData(ModelBuilder modelBuilder)
        {
            // إدراج أنواع الوثائق الأساسية
            modelBuilder.Entity<DocumentType>().HasData(
                new DocumentType { DocumentTypeID = 1, TypeName = "الرخصة التجارية", Description = "رخصة مزاولة النشاط التجاري", IsRequired = true, ValidityPeriodMonths = 12 },
                new DocumentType { DocumentTypeID = 2, TypeName = "شهادة الملاءة المالية", Description = "شهادة من البنك تؤكد الوضع المالي", IsRequired = true, ValidityPeriodMonths = 6 },
                new DocumentType { DocumentTypeID = 3, TypeName = "شهادة التأمين", Description = "شهادة تأمين ضد المخاطر", IsRequired = true, ValidityPeriodMonths = 12 },
                new DocumentType { DocumentTypeID = 4, TypeName = "السجل التجاري", Description = "السجل التجاري للشركة", IsRequired = true, ValidityPeriodMonths = 12 },
                new DocumentType { DocumentTypeID = 5, TypeName = "شهادة الجودة ISO", Description = "شهادة الجودة الدولية", IsRequired = false, ValidityPeriodMonths = 36 },
                new DocumentType { DocumentTypeID = 6, TypeName = "تصريح البلدية", Description = "تصريح من البلدية لمزاولة النشاط", IsRequired = true, ValidityPeriodMonths = 12 },
                new DocumentType { DocumentTypeID = 7, TypeName = "شهادة الضريبة", Description = "شهادة براءة ذمة ضريبية", IsRequired = true, ValidityPeriodMonths = 12 }
            );

            // إدراج فئات الموردين
            modelBuilder.Entity<SupplierCategory>().HasData(
                new SupplierCategory { CategoryID = 1, CategoryName = "مواد غذائية", Description = "موردو المواد الغذائية والمشروبات" },
                new SupplierCategory { CategoryID = 2, CategoryName = "مواد تنظيف", Description = "موردو مواد التنظيف والصيانة" },
                new SupplierCategory { CategoryID = 3, CategoryName = "أجهزة كهربائية", Description = "موردو الأجهزة الكهربائية والإلكترونية" },
                new SupplierCategory { CategoryID = 4, CategoryName = "أثاث ومفروشات", Description = "موردو الأثاث والمفروشات" },
                new SupplierCategory { CategoryID = 5, CategoryName = "قرطاسية ومكتبية", Description = "موردو القرطاسية واللوازم المكتبية" },
                new SupplierCategory { CategoryID = 6, CategoryName = "خدمات صيانة", Description = "مقدمو خدمات الصيانة والإصلاح" },
                new SupplierCategory { CategoryID = 7, CategoryName = "خدمات نقل", Description = "مقدمو خدمات النقل والتوصيل" },
                new SupplierCategory { CategoryID = 8, CategoryName = "مواد بناء", Description = "موردو مواد البناء والإنشاءات" }
            );

            // إدراج أعضاء لجنة المشتريات
            modelBuilder.Entity<ProcurementCommitteeMember>().HasData(
                new ProcurementCommitteeMember { CommitteeID = 1, MemberName = "أحمد محمد الصالح", Position = "رئيس لجنة المشتريات", Email = "<EMAIL>", PhoneNumber = "99887766", IsChairman = true, JoinDate = DateTime.Today },
                new ProcurementCommitteeMember { CommitteeID = 2, MemberName = "فاطمة علي الكندري", Position = "عضو لجنة المشتريات", Email = "<EMAIL>", PhoneNumber = "99887767", IsChairman = false, JoinDate = DateTime.Today },
                new ProcurementCommitteeMember { CommitteeID = 3, MemberName = "خالد سعد العتيبي", Position = "عضو لجنة المشتريات", Email = "<EMAIL>", PhoneNumber = "99887768", IsChairman = false, JoinDate = DateTime.Today },
                new ProcurementCommitteeMember { CommitteeID = 4, MemberName = "نورا حسن الرشيد", Position = "أمين سر اللجنة", Email = "<EMAIL>", PhoneNumber = "99887769", IsChairman = false, JoinDate = DateTime.Today }
            );
        }

        // إضافة سجل نشاط تلقائياً عند حفظ التغييرات
        public override int SaveChanges()
        {
            AddActivityLogs();
            return base.SaveChanges();
        }

        public override async Task<int> SaveChangesAsync(CancellationToken cancellationToken = default)
        {
            AddActivityLogs();
            return await base.SaveChangesAsync(cancellationToken);
        }

        private void AddActivityLogs()
        {
            var entries = ChangeTracker.Entries()
                .Where(e => e.Entity is not ActivityLog && e.State != EntityState.Unchanged)
                .ToList();

            foreach (var entry in entries)
            {
                var activityType = entry.State switch
                {
                    EntityState.Added => ActivityType.Create,
                    EntityState.Modified => ActivityType.Update,
                    EntityState.Deleted => ActivityType.Delete,
                    _ => "غير محدد"
                };

                var entityName = entry.Entity.GetType().Name;
                var description = $"{activityType} {entityName}";

                if (entry.Entity is Supplier supplier)
                {
                    ActivityLogs.Add(new ActivityLog
                    {
                        SupplierID = supplier.SupplierID,
                        ActivityType = activityType,
                        ActivityDescription = description,
                        PerformedBy = "النظام", // يمكن تحديث هذا ليكون المستخدم الحالي
                        PerformedDate = DateTime.Now
                    });
                }
            }
        }
    }
}
