using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace SupplierManagementSystem.Models
{
    /// <summary>
    /// نموذج بيانات تقييم المورد
    /// Supplier Evaluation Data Model
    /// </summary>
    public class SupplierEvaluation
    {
        [Key]
        public int EvaluationID { get; set; }

        [Required]
        [Display(Name = "معرف المورد")]
        public int SupplierID { get; set; }

        [Display(Name = "تاريخ التقييم")]
        public DateTime EvaluationDate { get; set; } = DateTime.Now;

        [Display(Name = "معرف المقيم")]
        public int? EvaluatorID { get; set; }

        [Required]
        [Range(0, 10, ErrorMessage = "درجة الجودة يجب أن تكون بين 0 و 10")]
        [Display(Name = "درجة الجودة")]
        public decimal QualityScore { get; set; }

        [Required]
        [Range(0, 10, ErrorMessage = "درجة التسليم يجب أن تكون بين 0 و 10")]
        [Display(Name = "درجة التسليم")]
        public decimal DeliveryScore { get; set; }

        [Required]
        [Range(0, 10, ErrorMessage = "درجة الخدمة يجب أن تكون بين 0 و 10")]
        [Display(Name = "درجة الخدمة")]
        public decimal ServiceScore { get; set; }

        [Required]
        [Range(0, 10, ErrorMessage = "درجة السعر يجب أن تكون بين 0 و 10")]
        [Display(Name = "درجة السعر")]
        public decimal PriceScore { get; set; }

        [Display(Name = "الدرجة الإجمالية")]
        public decimal OverallScore => (QualityScore + DeliveryScore + ServiceScore + PriceScore) / 4;

        [StringLength(1000)]
        [Display(Name = "التعليقات")]
        public string? Comments { get; set; }

        [StringLength(1000)]
        [Display(Name = "التوصيات")]
        public string? Recommendations { get; set; }

        [Display(Name = "نشط")]
        public bool IsActive { get; set; } = true;

        // Navigation Properties
        [ForeignKey("SupplierID")]
        public virtual Supplier? Supplier { get; set; }

        [ForeignKey("EvaluatorID")]
        public virtual ProcurementCommitteeMember? Evaluator { get; set; }

        // Computed Properties
        [NotMapped]
        [Display(Name = "تقدير التقييم")]
        public string Grade => OverallScore switch
        {
            >= 9 => "ممتاز",
            >= 8 => "جيد جداً",
            >= 7 => "جيد",
            >= 6 => "مقبول",
            >= 5 => "ضعيف",
            _ => "ضعيف جداً"
        };

        [NotMapped]
        [Display(Name = "لون التقييم")]
        public string GradeColor => OverallScore switch
        {
            >= 8 => "#10b981",
            >= 6 => "#f59e0b",
            >= 4 => "#ef4444",
            _ => "#dc2626"
        };

        // Methods
        public override string ToString()
        {
            return $"{Grade} ({OverallScore:F1}) - {EvaluationDate:yyyy/MM/dd}";
        }
    }
}
