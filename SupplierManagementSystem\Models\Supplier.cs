using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;

namespace SupplierManagementSystem.Models
{
    /// <summary>
    /// نموذج بيانات المورد
    /// Supplier Data Model
    /// </summary>
    public class Supplier
    {
        [Key]
        public int SupplierID { get; set; }

        [Required(ErrorMessage = "اسم المورد مطلوب")]
        [StringLength(200, ErrorMessage = "اسم المورد يجب أن يكون أقل من 200 حرف")]
        [Display(Name = "اسم المورد")]
        public string SupplierName { get; set; } = string.Empty;

        [StringLength(200, ErrorMessage = "الاسم التجاري يجب أن يكون أقل من 200 حرف")]
        [Display(Name = "الاسم التجاري")]
        public string? CommercialName { get; set; }

        [Required(ErrorMessage = "رقم الرخصة مطلوب")]
        [StringLength(50, ErrorMessage = "رقم الرخصة يجب أن يكون أقل من 50 حرف")]
        [Display(Name = "رقم الرخصة")]
        public string LicenseNumber { get; set; } = string.Empty;

        [StringLength(100, ErrorMessage = "اسم الشخص المسؤول يجب أن يكون أقل من 100 حرف")]
        [Display(Name = "الشخص المسؤول")]
        public string? ContactPerson { get; set; }

        [StringLength(20, ErrorMessage = "رقم الهاتف يجب أن يكون أقل من 20 حرف")]
        [Display(Name = "رقم الهاتف")]
        [Phone(ErrorMessage = "رقم الهاتف غير صحيح")]
        public string? PhoneNumber { get; set; }

        [StringLength(100, ErrorMessage = "البريد الإلكتروني يجب أن يكون أقل من 100 حرف")]
        [Display(Name = "البريد الإلكتروني")]
        [EmailAddress(ErrorMessage = "البريد الإلكتروني غير صحيح")]
        public string? Email { get; set; }

        [StringLength(500, ErrorMessage = "العنوان يجب أن يكون أقل من 500 حرف")]
        [Display(Name = "العنوان")]
        public string? Address { get; set; }

        [Required]
        [StringLength(20)]
        [Display(Name = "الحالة")]
        public string Status { get; set; } = "جديد";

        [Display(Name = "تاريخ الإنشاء")]
        public DateTime CreatedDate { get; set; } = DateTime.Now;

        [Display(Name = "تاريخ التعديل")]
        public DateTime ModifiedDate { get; set; } = DateTime.Now;

        [StringLength(100)]
        [Display(Name = "أنشئ بواسطة")]
        public string? CreatedBy { get; set; }

        [StringLength(100)]
        [Display(Name = "عُدل بواسطة")]
        public string? ModifiedBy { get; set; }

        [Display(Name = "نشط")]
        public bool IsActive { get; set; } = true;

        [StringLength(1000)]
        [Display(Name = "ملاحظات")]
        public string? Notes { get; set; }

        // Navigation Properties
        public virtual ICollection<SupplierDocument> Documents { get; set; } = new List<SupplierDocument>();
        public virtual ICollection<AccreditationRecord> AccreditationRecords { get; set; } = new List<AccreditationRecord>();
        public virtual ICollection<SupplierEvaluation> Evaluations { get; set; } = new List<SupplierEvaluation>();
        public virtual ICollection<SupplierCategoryMapping> CategoryMappings { get; set; } = new List<SupplierCategoryMapping>();
        public virtual ICollection<ActivityLog> ActivityLogs { get; set; } = new List<ActivityLog>();

        // Computed Properties
        [NotMapped]
        [Display(Name = "عدد الوثائق")]
        public int DocumentCount => Documents?.Count ?? 0;

        [NotMapped]
        [Display(Name = "آخر تقييم")]
        public DateTime? LastEvaluationDate => Evaluations?.OrderByDescending(e => e.EvaluationDate).FirstOrDefault()?.EvaluationDate;

        [NotMapped]
        [Display(Name = "متوسط التقييم")]
        public decimal? AverageScore => Evaluations?.Where(e => e.IsActive).Average(e => e.OverallScore);

        [NotMapped]
        [Display(Name = "لون الحالة")]
        public string StatusColor => Status switch
        {
            "معتمد" => "#10b981",
            "قيد المراجعة" => "#f59e0b",
            "جديد" => "#3b82f6",
            "مرفوض" => "#ef4444",
            "معلق" => "#6b7280",
            _ => "#6b7280"
        };

        [NotMapped]
        [Display(Name = "أيقونة الحالة")]
        public string StatusIcon => Status switch
        {
            "معتمد" => "✅",
            "قيد المراجعة" => "⏳",
            "جديد" => "🆕",
            "مرفوض" => "❌",
            "معلق" => "⏸️",
            _ => "❓"
        };

        // Methods
        public bool HasExpiredDocuments()
        {
            return Documents?.Any(d => d.IsActive && d.ExpiryDate.HasValue && d.ExpiryDate.Value < DateTime.Today) ?? false;
        }

        public bool HasExpiringDocuments(int daysAhead = 30)
        {
            var futureDate = DateTime.Today.AddDays(daysAhead);
            return Documents?.Any(d => d.IsActive && d.ExpiryDate.HasValue && 
                                     d.ExpiryDate.Value >= DateTime.Today && 
                                     d.ExpiryDate.Value <= futureDate) ?? false;
        }

        public IEnumerable<SupplierDocument> GetExpiringDocuments(int daysAhead = 30)
        {
            var futureDate = DateTime.Today.AddDays(daysAhead);
            return Documents?.Where(d => d.IsActive && d.ExpiryDate.HasValue && 
                                       d.ExpiryDate.Value >= DateTime.Today && 
                                       d.ExpiryDate.Value <= futureDate) ?? Enumerable.Empty<SupplierDocument>();
        }

        public bool CanBeApproved()
        {
            // يمكن اعتماد المورد إذا كان لديه جميع الوثائق المطلوبة وسارية المفعول
            // Supplier can be approved if they have all required documents that are valid
            return Status != "معتمد" && !HasExpiredDocuments();
        }

        public override string ToString()
        {
            return $"{SupplierName} ({LicenseNumber})";
        }
    }

    /// <summary>
    /// تعداد حالات المورد
    /// Supplier Status Enumeration
    /// </summary>
    public static class SupplierStatus
    {
        public const string New = "جديد";
        public const string UnderReview = "قيد المراجعة";
        public const string Approved = "معتمد";
        public const string Rejected = "مرفوض";
        public const string Suspended = "معلق";

        public static readonly string[] AllStatuses = { New, UnderReview, Approved, Rejected, Suspended };

        public static string GetDisplayName(string status)
        {
            return status switch
            {
                New => "جديد",
                UnderReview => "قيد المراجعة",
                Approved => "معتمد",
                Rejected => "مرفوض",
                Suspended => "معلق",
                _ => "غير محدد"
            };
        }
    }
}
