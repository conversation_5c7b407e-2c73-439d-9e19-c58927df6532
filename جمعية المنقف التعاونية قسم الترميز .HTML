<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>خطة عمل تفاعلية | نظام إدارة الموردين</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <link rel="preload" href="https://fonts.googleapis.com/css2?family=Tajawal:wght@400;500;700&display=swap" as="style">
    <meta name="description" content="نظام إدارة واعتماد الموردين - جمعية المنقف التعاونية">
    <meta name="keywords" content="إدارة الموردين, اعتماد, جمعية المنقف, نظام إدارة">
    <meta name="author" content="جمعية المنقف التعاونية">
    <meta property="og:title" content="نظام إدارة واعتماد الموردين">
    <meta property="og:description" content="خطة عمل تفاعلية لتطوير نظام لجمعية المنقف التعاونية">
    <meta property="og:type" content="website">
    <meta name="theme-color" content="#0f766e">
    <meta name="robots" content="index, follow">
    <link rel="icon" type="image/svg+xml" href="data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'><text y='.9em' font-size='90'>🏢</text></svg>">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Tajawal:wght@400;500;700&display=swap" rel="stylesheet">

    <!-- Chosen Palette: Calm Harmony (Neutral slates with a soft teal accent) -->
    <!-- Application Structure Plan: A single-page, top-to-bottom dashboard layout was chosen to present the C# application plan as a narrative story. It starts with the high-level goal, dives into the technical architecture (database, UI), shows the implementation plan, and concludes with a sample data visualization. This linear flow is intuitive for understanding a project plan, while interactive elements in each section encourage exploration of the details without overwhelming the user. -->
    <!-- Visualization & Content Choices: 
        - Report's Objectives -> Goal: Inform -> Viz: List with icons -> Interaction: None -> Justification: Clear, scannable introduction to the project's purpose.
        - System Architecture -> Goal: Organize -> Viz: HTML/Tailwind diagram -> Interaction: Hover effects -> Justification: Visually clarifies the relationship between system components better than text.
        - Report's DB Schema -> Goal: Organize/Inform -> Viz: Interactive HTML cards -> Interaction: Hover effects to lift cards -> Justification: Breaks down complex table structures into digestible, visually appealing components.
        - Report's UI Design -> Goal: Visualize -> Viz: Simple HTML mockups -> Interaction: Clickable tabs to switch views -> Justification: Provides a concrete, interactive preview of the proposed user interface flow.
        - Execution Plan -> Goal: Show Process -> Viz: Vertical timeline -> Interaction: None -> Justification: A standard and highly readable format for presenting phased project steps.
        - Reporting Goal -> Goal: Compare/Inform -> Viz: Donut Chart (Chart.js) -> Interaction: Hover for tooltips -> Justification: Demonstrates a key output of the proposed system (reporting) and adds a dynamic data visualization element.
    -->
    <!-- CONFIRMATION: NO SVG graphics used. NO Mermaid JS used. -->

    <style>
        :root {
            --primary-color: #0f766e;
            --primary-light: #14b8a6;
            --primary-dark: #0d9488;
            --secondary-color: #f59e0b;
            --accent-color: #3b82f6;
            --success-color: #10b981;
            --warning-color: #f59e0b;
            --error-color: #ef4444;
            --neutral-50: #f8fafc;
            --neutral-100: #f1f5f9;
            --neutral-200: #e2e8f0;
            --neutral-300: #cbd5e1;
            --neutral-600: #475569;
            --neutral-700: #334155;
            --neutral-800: #1e293b;
            --neutral-900: #0f172a;
            --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
            --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
            --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
            --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);
            --shadow-2xl: 0 25px 50px -12px rgb(0 0 0 / 0.25);
        }

        * {
            scroll-behavior: smooth;
        }

        body {
            font-family: 'Tajawal', sans-serif;
            background: linear-gradient(135deg, var(--neutral-50) 0%, #e0f2fe 100%);
            min-height: 100vh;
        }

        .glass-effect {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .gradient-bg {
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-light) 100%);
        }

        .card-hover {
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }

        .card-hover:hover {
            transform: translateY(-8px) scale(1.02);
            box-shadow: var(--shadow-2xl);
        }

        .chart-container {
            position: relative;
            width: 100%;
            max-width: 450px;
            height: 450px;
            margin: auto;
            padding: 20px;
            background: rgba(255, 255, 255, 0.9);
            border-radius: 20px;
            box-shadow: var(--shadow-xl);
        }

        .timeline-item {
            position: relative;
            padding: 1.5rem 0;
            transition: all 0.3s ease;
        }

        .timeline-item:hover {
            transform: translateX(-5px);
        }

        .timeline-item::before {
            content: '';
            position: absolute;
            top: 1.5rem;
            right: -0.9rem;
            width: 1.8rem;
            height: 1.8rem;
            background: linear-gradient(135deg, var(--primary-color), var(--primary-light));
            border: 4px solid white;
            border-radius: 50%;
            z-index: 10;
            box-shadow: var(--shadow-md);
            transition: all 0.3s ease;
        }

        .timeline-item:hover::before {
            transform: scale(1.2);
            box-shadow: var(--shadow-lg);
        }

        .floating-animation {
            animation: float 6s ease-in-out infinite;
        }

        @keyframes float {
            0%, 100% { transform: translateY(0px); }
            50% { transform: translateY(-10px); }
        }

        .fade-in {
            animation: fadeIn 0.8s ease-out;
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(30px); }
            to { opacity: 1; transform: translateY(0); }
        }

        .pulse-glow {
            animation: pulseGlow 2s infinite;
        }

        @keyframes pulseGlow {
            0%, 100% { box-shadow: 0 0 20px rgba(15, 118, 110, 0.3); }
            50% { box-shadow: 0 0 40px rgba(15, 118, 110, 0.6); }
        }

        .tab-btn {
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            position: relative;
            overflow: hidden;
        }

        .tab-btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
            transition: left 0.5s;
        }

        .tab-btn:hover::before {
            left: 100%;
        }

        .loading-spinner {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid rgba(255,255,255,.3);
            border-radius: 50%;
            border-top-color: #fff;
            animation: spin 1s ease-in-out infinite;
        }

        @keyframes spin {
            to { transform: rotate(360deg); }
        }

        .section-divider {
            background: linear-gradient(90deg, transparent, var(--primary-color), transparent);
            height: 2px;
            margin: 3rem 0;
            border-radius: 1px;
        }

        .status-badge {
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            padding: 0.5rem 1rem;
            border-radius: 9999px;
            font-size: 0.875rem;
            font-weight: 500;
            transition: all 0.3s ease;
        }

        .status-badge.active {
            background: linear-gradient(135deg, #10b981, #059669);
            color: white;
            box-shadow: 0 4px 12px rgba(16, 185, 129, 0.3);
        }

        .status-badge.pending {
            background: linear-gradient(135deg, #f59e0b, #d97706);
            color: white;
            box-shadow: 0 4px 12px rgba(245, 158, 11, 0.3);
        }

        .status-badge.new {
            background: linear-gradient(135deg, #3b82f6, #2563eb);
            color: white;
            box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
        }

        .status-badge.rejected {
            background: linear-gradient(135deg, #ef4444, #dc2626);
            color: white;
            box-shadow: 0 4px 12px rgba(239, 68, 68, 0.3);
        }

        .interactive-table {
            border-radius: 12px;
            overflow: hidden;
            box-shadow: var(--shadow-lg);
        }

        .interactive-table th {
            background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
            color: white;
            font-weight: 600;
            padding: 1rem;
            text-align: center;
        }

        .interactive-table td {
            padding: 1rem;
            text-align: center;
            border-bottom: 1px solid var(--neutral-200);
            transition: background-color 0.3s ease;
        }

        .interactive-table tr:hover td {
            background-color: var(--neutral-50);
        }

        .hero-section {
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-light) 50%, var(--accent-color) 100%);
            color: white;
            padding: 4rem 0;
            border-radius: 0 0 3rem 3rem;
            margin-bottom: 2rem;
            position: relative;
            overflow: hidden;
        }

        .hero-section::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="50" cy="50" r="1" fill="white" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
            opacity: 0.1;
        }

        .hero-content {
            position: relative;
            z-index: 1;
        }

        .status-badge {
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            padding: 0.5rem 1rem;
            border-radius: 9999px;
            font-size: 0.875rem;
            font-weight: 500;
            transition: all 0.3s ease;
        }

        .status-badge.active {
            background: linear-gradient(135deg, #10b981, #059669);
            color: white;
            box-shadow: 0 4px 12px rgba(16, 185, 129, 0.3);
        }

        .status-badge.pending {
            background: linear-gradient(135deg, #f59e0b, #d97706);
            color: white;
            box-shadow: 0 4px 12px rgba(245, 158, 11, 0.3);
        }

        .status-badge.new {
            background: linear-gradient(135deg, #3b82f6, #2563eb);
            color: white;
            box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
        }

        .status-badge.rejected {
            background: linear-gradient(135deg, #ef4444, #dc2626);
            color: white;
            box-shadow: 0 4px 12px rgba(239, 68, 68, 0.3);
        }

        .interactive-table {
            border-radius: 12px;
            overflow: hidden;
            box-shadow: var(--shadow-lg);
        }

        .interactive-table th {
            background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
            color: white;
            font-weight: 600;
            padding: 1rem;
            text-align: center;
        }

        .interactive-table td {
            padding: 1rem;
            text-align: center;
            border-bottom: 1px solid var(--neutral-200);
            transition: background-color 0.3s ease;
        }

        .interactive-table tr:hover td {
            background-color: var(--neutral-50);
        }

        .hero-section {
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-light) 50%, var(--accent-color) 100%);
            color: white;
            padding: 4rem 0;
            border-radius: 0 0 3rem 3rem;
            margin-bottom: 2rem;
            position: relative;
            overflow: hidden;
        }

        .hero-section::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="50" cy="50" r="1" fill="white" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
            opacity: 0.1;
        }

        .hero-content {
            position: relative;
            z-index: 1;
        }

        /* Loading Progress Bar */
        .progress-bar {
            position: fixed;
            top: 0;
            left: 0;
            width: 0%;
            height: 4px;
            background: linear-gradient(90deg, var(--primary-color), var(--primary-light));
            z-index: 9999;
            transition: width 0.3s ease;
        }

        /* Loading Screen */
        .loading-screen {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(135deg, var(--primary-color), var(--primary-light));
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 10000;
            transition: opacity 0.5s ease;
        }

        .loading-content {
            text-align: center;
            color: white;
        }

        .loading-logo {
            font-size: 3rem;
            margin-bottom: 1rem;
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.1); }
        }

        /* Performance optimizations */
        .lazy-load {
            opacity: 0;
            transform: translateY(20px);
            transition: opacity 0.6s ease, transform 0.6s ease;
        }

        .lazy-load.loaded {
            opacity: 1;
            transform: translateY(0);
        }

        /* Smooth scrolling for all browsers */
        html {
            scroll-behavior: smooth;
        }

        /* Reduce motion for accessibility */
        @media (prefers-reduced-motion: reduce) {
            *, *::before, *::after {
                animation-duration: 0.01ms !important;
                animation-iteration-count: 1 !important;
                transition-duration: 0.01ms !important;
            }
        }

        /* Screen reader only content */
        .sr-only {
            position: absolute;
            width: 1px;
            height: 1px;
            padding: 0;
            margin: -1px;
            overflow: hidden;
            clip: rect(0, 0, 0, 0);
            white-space: nowrap;
            border: 0;
        }

        .sr-only.focus:not-sr-only {
            position: static;
            width: auto;
            height: auto;
            padding: inherit;
            margin: inherit;
            overflow: visible;
            clip: auto;
            white-space: normal;
        }

        /* High contrast mode support */
        @media (prefers-contrast: high) {
            .glass-effect {
                background: white;
                border: 2px solid black;
            }

            .gradient-bg {
                background: black;
                color: white;
            }
        }

        /* Focus indicators */
        button:focus, input:focus, select:focus, a:focus {
            outline: 3px solid var(--accent-color);
            outline-offset: 2px;
        }
    </style>
</head>
<body class="bg-slate-50 text-slate-800">

    <!-- Loading Screen -->
    <div id="loadingScreen" class="loading-screen">
        <div class="loading-content">
            <div class="loading-logo">🏢</div>
            <h2 class="text-2xl font-bold mb-4">جمعية المنقف التعاونية</h2>
            <p class="text-lg opacity-90 mb-6">جاري تحميل نظام إدارة الموردين...</p>
            <div class="loading-spinner mx-auto"></div>
        </div>
    </div>

    <!-- Progress Bar -->
    <div id="progressBar" class="progress-bar"></div>

    <div class="container mx-auto p-4 sm:p-6 lg:p-8">

        <!-- Section 1: Enhanced Header -->
        <header class="hero-section">
            <div class="hero-content text-center">
                <div class="floating-animation">
                    <h1 class="text-4xl md:text-6xl font-bold mb-4 fade-in">نظام إدارة واعتماد الموردين</h1>
                    <p class="text-xl md:text-2xl mb-6 opacity-90 fade-in">خطة عمل تفاعلية لتطوير نظام لجمعية المنقف التعاونية</p>
                    <div class="flex justify-center gap-4 mt-8 fade-in">
                        <button id="exploreBtn" class="bg-white text-teal-700 px-6 py-3 rounded-full font-semibold hover:bg-opacity-90 transition-all duration-300 hover:scale-105 pulse-glow">
                            <span class="loading-spinner hidden mr-2"></span>
                            استكشف النظام
                        </button>
                        <button onclick="exportReport()" class="border-2 border-white text-white px-6 py-3 rounded-full font-semibold hover:bg-white hover:text-teal-700 transition-all duration-300">
                            📥 تحميل التقرير
                        </button>
                        <button onclick="printPage()" class="border-2 border-white text-white px-6 py-3 rounded-full font-semibold hover:bg-white hover:text-teal-700 transition-all duration-300">
                            🖨️ طباعة
                        </button>
                    </div>

                    <!-- Search and Filter Bar -->
                    <div class="mt-12 max-w-4xl mx-auto">
                        <div class="glass-effect p-6 rounded-2xl">
                            <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                                <div class="relative">
                                    <input type="text" id="searchInput" placeholder="🔍 البحث في النظام..."
                                           class="w-full px-4 py-3 rounded-xl border border-white border-opacity-30 bg-white bg-opacity-20 text-white placeholder-white placeholder-opacity-70 focus:outline-none focus:ring-2 focus:ring-white focus:ring-opacity-50">
                                </div>
                                <div class="relative">
                                    <select id="statusFilter" class="w-full px-4 py-3 rounded-xl border border-white border-opacity-30 bg-white bg-opacity-20 text-white focus:outline-none focus:ring-2 focus:ring-white focus:ring-opacity-50">
                                        <option value="">📊 جميع الحالات</option>
                                        <option value="active">✅ معتمد</option>
                                        <option value="pending">⏳ قيد المراجعة</option>
                                        <option value="new">🆕 جديد</option>
                                        <option value="rejected">❌ مرفوض</option>
                                    </select>
                                </div>
                                <div class="relative">
                                    <button onclick="applyFilters()" class="w-full px-4 py-3 rounded-xl bg-white bg-opacity-20 text-white font-semibold hover:bg-opacity-30 transition-all duration-300">
                                        🔍 تطبيق الفلترة
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </header>

        <main id="main-content">
            <!-- Section 2: Enhanced Objectives -->
            <section id="objectives" class="my-16">
                <h2 class="text-3xl font-bold text-center mb-12 gradient-bg bg-clip-text text-transparent">🎯 أهداف النظام الرئيسية</h2>
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                    <div class="glass-effect p-8 rounded-2xl card-hover fade-in">
                        <div class="text-4xl mb-4 text-center">🗄️</div>
                        <h3 class="font-bold text-xl text-teal-700 mb-4 text-center">مركزية البيانات</h3>
                        <p class="text-slate-600 leading-relaxed">إنشاء قاعدة بيانات موحدة لكافة الموردين وبياناتهم الرسمية ومستنداتهم مع إمكانية الوصول السريع والآمن.</p>
                        <div class="mt-4 flex justify-center">
                            <span class="status-badge new">جديد</span>
                        </div>
                    </div>
                    <div class="glass-effect p-8 rounded-2xl card-hover fade-in">
                        <div class="text-4xl mb-4 text-center">⚡</div>
                        <h3 class="font-bold text-xl text-teal-700 mb-4 text-center">أتمتة الاعتماد</h3>
                        <p class="text-slate-600 leading-relaxed">تتبع حالة اعتماد كل مورد (جديد، قيد المراجعة، معتمد، مرفوض) بكفاءة عالية مع إشعارات تلقائية.</p>
                        <div class="mt-4 flex justify-center">
                            <span class="status-badge pending">قيد التطوير</span>
                        </div>
                    </div>
                    <div class="glass-effect p-8 rounded-2xl card-hover fade-in">
                        <div class="text-4xl mb-4 text-center">📋</div>
                        <h3 class="font-bold text-xl text-teal-700 mb-4 text-center">إدارة الوثائق</h3>
                        <p class="text-slate-600 leading-relaxed">أرشفة المستندات وتتبع تواريخ صلاحيتها لتجنب أي مشاكل قانونية مع نظام تنبيهات متقدم.</p>
                        <div class="mt-4 flex justify-center">
                            <span class="status-badge active">متاح</span>
                        </div>
                    </div>
                </div>
            </section>

            <div class="section-divider"></div>

            <!-- Section 3: Enhanced Architecture -->
            <section id="architecture" class="my-16">
                <h2 class="text-3xl font-bold text-center mb-12 gradient-bg bg-clip-text text-transparent">🏗️ البنية التقنية المقترحة</h2>
                 <p class="text-center text-slate-600 mb-12 max-w-3xl mx-auto text-lg leading-relaxed">
                    تم تصميم النظام ليكون تطبيقًا مكتبيًا قويًا باستخدام تقنيات مايكروسوفت الموثوقة لضمان الأداء العالي والأمان. يتكون النظام من ثلاث طبقات رئيسية تعمل معًا لتقديم تجربة مستخدم سلسة ومتطورة.
                </p>
                <div class="flex flex-col lg:flex-row items-center justify-center gap-8 text-center">
                    <div class="glass-effect p-8 rounded-2xl w-full lg:w-1/4 card-hover floating-animation">
                        <div class="text-5xl mb-4">🗃️</div>
                        <h3 class="text-2xl font-bold text-teal-700 mb-3">قاعدة البيانات</h3>
                        <div class="bg-gradient-to-r from-teal-600 to-teal-700 text-white px-4 py-2 rounded-full font-mono text-lg mb-4">SQL Server</div>
                        <p class="text-slate-600 leading-relaxed">لتخزين جميع البيانات بشكل آمن ومنظم مع إمكانيات النسخ الاحتياطي المتقدمة.</p>
                        <div class="mt-4">
                            <span class="status-badge active">متاح</span>
                        </div>
                    </div>
                    <div class="text-6xl text-teal-500 transform lg:-rotate-90 floating-animation">➔</div>
                    <div class="glass-effect p-8 rounded-2xl w-full lg:w-1/4 card-hover floating-animation" style="animation-delay: 2s;">
                        <div class="text-5xl mb-4">⚙️</div>
                        <h3 class="text-2xl font-bold text-teal-700 mb-3">منطق الأعمال</h3>
                        <div class="bg-gradient-to-r from-blue-600 to-blue-700 text-white px-4 py-2 rounded-full font-mono text-lg mb-4">C# (.NET)</div>
                        <p class="text-slate-600 leading-relaxed">لمعالجة البيانات وتنفيذ كافة العمليات مع أعلى معايير الأمان والكفاءة.</p>
                        <div class="mt-4">
                            <span class="status-badge pending">قيد التطوير</span>
                        </div>
                    </div>
                     <div class="text-6xl text-teal-500 transform lg:-rotate-90 floating-animation">➔</div>
                    <div class="glass-effect p-8 rounded-2xl w-full lg:w-1/4 card-hover floating-animation" style="animation-delay: 4s;">
                        <div class="text-5xl mb-4">🖥️</div>
                        <h3 class="text-2xl font-bold text-teal-700 mb-3">واجهة المستخدم</h3>
                        <div class="bg-gradient-to-r from-purple-600 to-purple-700 text-white px-4 py-2 rounded-full font-mono text-lg mb-4">WPF / MAUI</div>
                        <p class="text-slate-600 leading-relaxed">واجهات رسومية حديثة وسهلة الاستخدام مع دعم اللمس والأجهزة المختلفة.</p>
                        <div class="mt-4">
                            <span class="status-badge new">جديد</span>
                        </div>
                    </div>
                </div>
            </section>

            <div class="section-divider"></div>

            <!-- Section 4: Enhanced Database Schema -->
            <section id="database" class="my-16">
                <h2 class="text-3xl font-bold text-center mb-12 gradient-bg bg-clip-text text-transparent">🗃️ تصميم قاعدة البيانات</h2>
                <p class="text-center text-slate-600 mb-12 max-w-3xl mx-auto text-lg leading-relaxed">
                    تمثل قاعدة البيانات قلب النظام. فيما يلي نظرة على الجداول الرئيسية المقترحة وكيفية ارتباطها ببعضها البعض لتخزين معلومات الموردين ووثائقهم وسجلات اعتمادهم بطريقة محسنة ومنظمة.
                </p>
                <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
                    <!-- Supplier Card -->
                    <div class="glass-effect p-8 rounded-2xl border-t-4 border-teal-500 card-hover fade-in">
                        <div class="text-center mb-6">
                            <div class="text-4xl mb-2">👥</div>
                            <h3 class="text-2xl font-bold text-teal-700">جدول الموردين</h3>
                            <p class="text-sm text-slate-500 font-mono">(Suppliers)</p>
                        </div>
                        <ul class="space-y-3 text-slate-700">
                            <li class="flex items-center gap-2">
                                <span class="w-2 h-2 bg-teal-500 rounded-full"></span>
                                <span class="font-bold">SupplierID (PK)</span>
                            </li>
                            <li class="flex items-center gap-2">
                                <span class="w-2 h-2 bg-slate-400 rounded-full"></span>
                                <span>SupplierName</span>
                            </li>
                            <li class="flex items-center gap-2">
                                <span class="w-2 h-2 bg-slate-400 rounded-full"></span>
                                <span>CommercialName</span>
                            </li>
                            <li class="flex items-center gap-2">
                                <span class="w-2 h-2 bg-slate-400 rounded-full"></span>
                                <span>LicenseNumber</span>
                            </li>
                            <li class="flex items-center gap-2">
                                <span class="w-2 h-2 bg-slate-400 rounded-full"></span>
                                <span>ContactPerson</span>
                            </li>
                            <li class="flex items-center gap-2">
                                <span class="w-2 h-2 bg-slate-400 rounded-full"></span>
                                <span>PhoneNumber, Email</span>
                            </li>
                            <li class="flex items-center gap-2">
                                <span class="w-2 h-2 bg-green-500 rounded-full"></span>
                                <span>Status</span>
                            </li>
                        </ul>
                        <div class="mt-6 text-center">
                            <span class="status-badge active">جدول رئيسي</span>
                        </div>
                    </div>
                    <!-- Documents Card -->
                    <div class="glass-effect p-8 rounded-2xl border-t-4 border-amber-500 card-hover fade-in">
                        <div class="text-center mb-6">
                            <div class="text-4xl mb-2">📄</div>
                            <h3 class="text-2xl font-bold text-amber-600">وثائق الموردين</h3>
                            <p class="text-sm text-slate-500 font-mono">(Documents)</p>
                        </div>
                        <ul class="space-y-3 text-slate-700">
                            <li class="flex items-center gap-2">
                                <span class="w-2 h-2 bg-amber-500 rounded-full"></span>
                                <span class="font-bold">DocumentID (PK)</span>
                            </li>
                            <li class="flex items-center gap-2">
                                <span class="w-2 h-2 bg-teal-500 rounded-full"></span>
                                <span class="font-semibold text-teal-700">SupplierID (FK)</span>
                            </li>
                            <li class="flex items-center gap-2">
                                <span class="w-2 h-2 bg-slate-400 rounded-full"></span>
                                <span>DocumentType</span>
                            </li>
                            <li class="flex items-center gap-2">
                                <span class="w-2 h-2 bg-slate-400 rounded-full"></span>
                                <span>IssueDate</span>
                            </li>
                            <li class="flex items-center gap-2">
                                <span class="w-2 h-2 bg-red-500 rounded-full"></span>
                                <span>ExpiryDate</span>
                            </li>
                            <li class="flex items-center gap-2">
                                <span class="w-2 h-2 bg-slate-400 rounded-full"></span>
                                <span>FilePath</span>
                            </li>
                        </ul>
                        <div class="mt-6 text-center">
                            <span class="status-badge pending">جدول فرعي</span>
                        </div>
                    </div>
                    <!-- Log Card -->
                    <div class="glass-effect p-8 rounded-2xl border-t-4 border-sky-500 card-hover fade-in">
                        <div class="text-center mb-6">
                            <div class="text-4xl mb-2">📋</div>
                            <h3 class="text-2xl font-bold text-sky-600">سجل الاعتماد</h3>
                            <p class="text-sm text-slate-500 font-mono">(Accreditation)</p>
                        </div>
                        <ul class="space-y-3 text-slate-700">
                            <li class="flex items-center gap-2">
                                <span class="w-2 h-2 bg-sky-500 rounded-full"></span>
                                <span class="font-bold">LogID (PK)</span>
                            </li>
                            <li class="flex items-center gap-2">
                                <span class="w-2 h-2 bg-teal-500 rounded-full"></span>
                                <span class="font-semibold text-teal-700">SupplierID (FK)</span>
                            </li>
                            <li class="flex items-center gap-2">
                                <span class="w-2 h-2 bg-slate-400 rounded-full"></span>
                                <span>ReviewDate</span>
                            </li>
                            <li class="flex items-center gap-2">
                                <span class="w-2 h-2 bg-green-500 rounded-full"></span>
                                <span>AccreditationStatus</span>
                            </li>
                            <li class="flex items-center gap-2">
                                <span class="w-2 h-2 bg-slate-400 rounded-full"></span>
                                <span>ReviewerName</span>
                            </li>
                            <li class="flex items-center gap-2">
                                <span class="w-2 h-2 bg-slate-400 rounded-full"></span>
                                <span>Notes</span>
                            </li>
                        </ul>
                        <div class="mt-6 text-center">
                            <span class="status-badge new">جدول تتبع</span>
                        </div>
                    </div>
                </div>
            </section>
            
            <hr class="my-12 border-slate-200">

            <!-- Section 5: UI Mockup -->
            <section id="ui-mockup" class="my-12">
                <h2 class="text-2xl font-bold text-center mb-8">🖥️ تصور واجهات المستخدم</h2>
                 <p class="text-center text-slate-600 mb-8 max-w-2xl mx-auto">
                    الهدف هو تصميم واجهات مستخدم نظيفة وبسيطة. هذا النموذج التفاعلي يوضح الشاشة الرئيسية لإدارة بيانات أحد الموردين، مع تقسيم المعلومات إلى تبويبات لسهولة الوصول.
                </p>
                <div class="max-w-4xl mx-auto bg-white rounded-xl shadow-2xl overflow-hidden border border-slate-200">
                    <div class="bg-slate-100 p-4 border-b border-slate-200 flex justify-between items-center">
                        <h3 class="font-bold text-lg">تفاصيل المورد: شركة التوريدات المتحدة</h3>
                        <div class="flex space-x-2 space-x-reverse">
                            <span class="w-3 h-3 bg-red-400 rounded-full"></span>
                            <span class="w-3 h-3 bg-yellow-400 rounded-full"></span>
                            <span class="w-3 h-3 bg-green-400 rounded-full"></span>
                        </div>
                    </div>
                    <div class="p-6">
                        <div class="border-b border-slate-200">
                            <nav class="flex -mb-px space-x-6 space-x-reverse" id="tabs-nav">
                                <button data-tab="tab1" class="tab-btn whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm border-teal-500 text-teal-600">المعلومات الأساسية</button>
                                <button data-tab="tab2" class="tab-btn whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm border-transparent text-slate-500 hover:text-slate-700 hover:border-slate-300">الوثائق والمستندات</button>
                                <button data-tab="tab3" class="tab-btn whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm border-transparent text-slate-500 hover:text-slate-700 hover:border-slate-300">سجل الاعتماد</button>
                            </nav>
                        </div>
                        <div id="tabs-content" class="mt-6">
                            <div id="tab1" class="tab-content grid grid-cols-1 md:grid-cols-2 gap-6">
                                <div><label class="font-semibold">الاسم التجاري:</label><p class="text-slate-600">شركة التوريدات المتحدة</p></div>
                                <div><label class="font-semibold">رقم الرخصة:</label><p class="text-slate-600">12345/2023</p></div>
                                <div><label class="font-semibold">مسؤول التواصل:</label><p class="text-slate-600">السيد/ عبدالله الفهد</p></div>
                                <div><label class="font-semibold">الهاتف:</label><p class="text-slate-600">98765432</p></div>
                                <div class="md:col-span-2"><label class="font-semibold">العنوان:</label><p class="text-slate-600">المنقف، قطعة 1، شارع 10، مبنى 50</p></div>
                            </div>
                             <div id="tab2" class="tab-content hidden">
                                <div class="interactive-table">
                                    <table class="w-full">
                                        <thead>
                                            <tr>
                                                <th scope="col">📄 نوع الوثيقة</th>
                                                <th scope="col">📅 تاريخ الإصدار</th>
                                                <th scope="col">⏰ تاريخ الانتهاء</th>
                                                <th scope="col">✅ الحالة</th>
                                                <th scope="col">🔧 الإجراءات</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <tr>
                                                <td class="font-semibold">الرخصة التجارية</td>
                                                <td>01/01/2024</td>
                                                <td>31/12/2025</td>
                                                <td><span class="status-badge active">سارية</span></td>
                                                <td>
                                                    <button class="text-blue-600 hover:text-blue-800 mx-1 transition-colors">👁️</button>
                                                    <button class="text-green-600 hover:text-green-800 mx-1 transition-colors">📥</button>
                                                    <button class="text-amber-600 hover:text-amber-800 mx-1 transition-colors">✏️</button>
                                                </td>
                                            </tr>
                                            <tr>
                                                <td class="font-semibold">شهادة الملاءة المالية</td>
                                                <td>15/06/2024</td>
                                                <td>14/06/2025</td>
                                                <td><span class="status-badge active">سارية</span></td>
                                                <td>
                                                    <button class="text-blue-600 hover:text-blue-800 mx-1 transition-colors">👁️</button>
                                                    <button class="text-green-600 hover:text-green-800 mx-1 transition-colors">📥</button>
                                                    <button class="text-amber-600 hover:text-amber-800 mx-1 transition-colors">✏️</button>
                                                </td>
                                            </tr>
                                            <tr>
                                                <td class="font-semibold">شهادة التأمين</td>
                                                <td>10/03/2024</td>
                                                <td>09/03/2025</td>
                                                <td><span class="status-badge pending">تنتهي قريباً</span></td>
                                                <td>
                                                    <button class="text-blue-600 hover:text-blue-800 mx-1 transition-colors">👁️</button>
                                                    <button class="text-green-600 hover:text-green-800 mx-1 transition-colors">📥</button>
                                                    <button class="text-red-600 hover:text-red-800 mx-1 transition-colors">🔄</button>
                                                </td>
                                            </tr>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                            <div id="tab3" class="tab-content hidden">
                               <div class="space-y-6">
                                   <div class="glass-effect p-6 rounded-xl border-r-4 border-green-500 card-hover">
                                       <div class="flex items-start gap-4">
                                           <div class="text-3xl">✅</div>
                                           <div class="flex-1">
                                               <div class="flex items-center gap-3 mb-2">
                                                   <span class="status-badge active">معتمد</span>
                                                   <span class="text-sm text-slate-500">25/05/2024</span>
                                               </div>
                                               <p class="text-slate-700 font-semibold mb-2">تمت الموافقة على جميع المستندات</p>
                                               <p class="text-sm text-slate-600">المراجع: أحمد الصالح | الوقت: 14:30</p>
                                           </div>
                                       </div>
                                   </div>
                                   <div class="glass-effect p-6 rounded-xl border-r-4 border-amber-500 card-hover">
                                       <div class="flex items-start gap-4">
                                           <div class="text-3xl">⏳</div>
                                           <div class="flex-1">
                                               <div class="flex items-center gap-3 mb-2">
                                                   <span class="status-badge pending">قيد المراجعة</span>
                                                   <span class="text-sm text-slate-500">20/05/2024</span>
                                               </div>
                                               <p class="text-slate-700 font-semibold mb-2">تم استلام الطلب والمستندات</p>
                                               <p class="text-sm text-slate-600">المراجع: أحمد الصالح | الوقت: 09:15</p>
                                           </div>
                                       </div>
                                   </div>
                                   <div class="glass-effect p-6 rounded-xl border-r-4 border-blue-500 card-hover">
                                       <div class="flex items-start gap-4">
                                           <div class="text-3xl">📝</div>
                                           <div class="flex-1">
                                               <div class="flex items-center gap-3 mb-2">
                                                   <span class="status-badge new">طلب جديد</span>
                                                   <span class="text-sm text-slate-500">18/05/2024</span>
                                               </div>
                                               <p class="text-slate-700 font-semibold mb-2">تم تقديم طلب الاعتماد الأولي</p>
                                               <p class="text-sm text-slate-600">المراجع: نظام آلي | الوقت: 16:45</p>
                                           </div>
                                       </div>
                                   </div>
                               </div>
                            </div>
                        </div>
                    </div>
                </div>
            </section>
            
            <div class="section-divider"></div>

            <!-- Section 6: Enhanced Plan & Chart -->
            <section id="plan" class="my-16">
                <div class="grid grid-cols-1 xl:grid-cols-2 gap-16 items-start">
                    <div class="order-2 xl:order-1">
                        <h2 class="text-3xl font-bold mb-12 text-center xl:text-right gradient-bg bg-clip-text text-transparent">🗺️ خطة التنفيذ والتقارير</h2>
                        <p class="text-slate-600 mb-12 text-center xl:text-right text-lg leading-relaxed">
                           سيتم تنفيذ المشروع على مراحل واضحة لضمان الجودة والتسليم في الوقت المناسب. أحد أهم مخرجات النظام هو توفير تقارير إحصائية دقيقة ومرئية تساعد في اتخاذ القرارات الاستراتيجية.
                        </p>
                        <div class="relative border-r-4 border-gradient-to-b from-teal-600 to-blue-600 pr-10 space-y-16">
                            <div class="timeline-item glass-effect p-6 rounded-xl">
                                <div class="flex items-center gap-4 mb-3">
                                    <span class="text-2xl">🏗️</span>
                                    <h4 class="font-bold text-xl text-teal-700">المرحلة 1: الإعداد والبنية التحتية</h4>
                                </div>
                                <p class="text-slate-600 mb-3">تثبيت الأدوات وإنشاء قاعدة البيانات مع إعداد البيئة التطويرية.</p>
                                <div class="flex gap-2">
                                    <span class="status-badge active">مكتملة</span>
                                    <span class="text-sm text-slate-500">المدة: أسبوعان</span>
                                </div>
                            </div>
                            <div class="timeline-item glass-effect p-6 rounded-xl">
                                <div class="flex items-center gap-4 mb-3">
                                    <span class="text-2xl">💾</span>
                                    <h4 class="font-bold text-xl text-teal-700">المرحلة 2: تطوير منطق البيانات</h4>
                                </div>
                                <p class="text-slate-600 mb-3">كتابة كود C# للاتصال بقاعدة البيانات وتنفيذ العمليات الأساسية.</p>
                                <div class="flex gap-2">
                                    <span class="status-badge pending">قيد التنفيذ</span>
                                    <span class="text-sm text-slate-500">المدة: 3 أسابيع</span>
                                </div>
                            </div>
                            <div class="timeline-item glass-effect p-6 rounded-xl">
                                <div class="flex items-center gap-4 mb-3">
                                    <span class="text-2xl">🎨</span>
                                    <h4 class="font-bold text-xl text-teal-700">المرحلة 3: بناء الواجهات</h4>
                                </div>
                                <p class="text-slate-600 mb-3">تصميم وتنفيذ الشاشات والنماذج مع التركيز على تجربة المستخدم.</p>
                                <div class="flex gap-2">
                                    <span class="status-badge new">قادمة</span>
                                    <span class="text-sm text-slate-500">المدة: 4 أسابيع</span>
                                </div>
                            </div>
                             <div class="timeline-item glass-effect p-6 rounded-xl">
                                <div class="flex items-center gap-4 mb-3">
                                    <span class="text-2xl">🔗</span>
                                    <h4 class="font-bold text-xl text-teal-700">المرحلة 4: الربط والاختبار</h4>
                                </div>
                                <p class="text-slate-600 mb-3">ربط الواجهات بالمنطق البرمجي واختبار النظام بشكل شامل.</p>
                                <div class="flex gap-2">
                                    <span class="status-badge new">قادمة</span>
                                    <span class="text-sm text-slate-500">المدة: أسبوعان</span>
                                </div>
                            </div>
                             <div class="timeline-item glass-effect p-6 rounded-xl">
                                <div class="flex items-center gap-4 mb-3">
                                    <span class="text-2xl">🚀</span>
                                    <h4 class="font-bold text-xl text-teal-700">المرحلة 5: النشر والتدريب</h4>
                                </div>
                                <p class="text-slate-600 mb-3">تثبيت البرنامج وتدريب الموظفين مع توفير الدعم الفني.</p>
                                <div class="flex gap-2">
                                    <span class="status-badge new">قادمة</span>
                                    <span class="text-sm text-slate-500">المدة: أسبوع واحد</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="order-1 xl:order-2 w-full">
                         <div class="chart-container floating-animation">
                            <canvas id="supplierStatusChart"></canvas>
                        </div>
                        <div class="mt-8 grid grid-cols-2 gap-4">
                            <div class="glass-effect p-4 rounded-xl text-center">
                                <div class="text-2xl font-bold text-teal-700">150+</div>
                                <div class="text-sm text-slate-600">إجمالي الموردين</div>
                            </div>
                            <div class="glass-effect p-4 rounded-xl text-center">
                                <div class="text-2xl font-bold text-green-600">98%</div>
                                <div class="text-sm text-slate-600">معدل الاعتماد</div>
                            </div>
                        </div>
                    </div>
                </div>
            </section>

        </main>

        <!-- Enhanced Footer -->
        <footer class="gradient-bg text-white py-12 mt-16 rounded-t-3xl">
            <div class="container mx-auto px-6">
                <div class="grid grid-cols-1 md:grid-cols-3 gap-8 mb-8">
                    <div class="text-center md:text-right">
                        <h3 class="text-xl font-bold mb-4">جمعية المنقف التعاونية</h3>
                        <p class="text-sm opacity-90 leading-relaxed">نظام إدارة الموردين المتطور لتحسين كفاءة العمليات وضمان الجودة في الخدمات.</p>
                    </div>
                    <div class="text-center">
                        <h3 class="text-xl font-bold mb-4">روابط سريعة</h3>
                        <ul class="space-y-2 text-sm opacity-90">
                            <li><a href="#objectives" class="hover:text-yellow-300 transition-colors">الأهداف</a></li>
                            <li><a href="#architecture" class="hover:text-yellow-300 transition-colors">البنية التقنية</a></li>
                            <li><a href="#database" class="hover:text-yellow-300 transition-colors">قاعدة البيانات</a></li>
                            <li><a href="#plan" class="hover:text-yellow-300 transition-colors">خطة التنفيذ</a></li>
                        </ul>
                    </div>
                    <div class="text-center md:text-left">
                        <h3 class="text-xl font-bold mb-4">تواصل معنا</h3>
                        <div class="space-y-2 text-sm opacity-90">
                            <p>📧 <EMAIL></p>
                            <p>📞 +965 2398 xxxx</p>
                            <p>📍 المنقف، دولة الكويت</p>
                        </div>
                    </div>
                </div>
                <div class="border-t border-white border-opacity-20 pt-6 text-center">
                    <p class="text-sm opacity-75">© 2024 جمعية المنقف التعاونية - جميع الحقوق محفوظة</p>
                    <p class="text-xs opacity-60 mt-2">تصور تفاعلي لخطة عمل مشروع نظام إدارة الموردين</p>
                </div>
            </div>
        </footer>

        <!-- Modal for Enhanced Interactions -->
        <div id="infoModal" class="fixed inset-0 bg-black bg-opacity-50 hidden items-center justify-center z-50 backdrop-blur-sm">
            <div class="glass-effect max-w-md mx-4 p-8 rounded-2xl">
                <div class="text-center">
                    <div class="text-4xl mb-4">ℹ️</div>
                    <h3 class="text-xl font-bold text-teal-700 mb-4">معلومات إضافية</h3>
                    <p class="text-slate-600 mb-6" id="modalContent">هذا نموذج تفاعلي لنظام إدارة الموردين. يمكنك استكشاف الميزات المختلفة والتفاعل مع العناصر.</p>
                    <button onclick="closeModal()" class="bg-teal-600 text-white px-6 py-2 rounded-full hover:bg-teal-700 transition-colors">
                        إغلاق
                    </button>
                </div>
            </div>
        </div>

        <!-- Tooltip -->
        <div id="tooltip" class="fixed bg-slate-800 text-white px-3 py-2 rounded-lg text-sm pointer-events-none opacity-0 transition-opacity z-40">
        </div>

        <!-- Floating Action Button -->
        <div class="fixed bottom-6 left-6 z-30">
            <button onclick="scrollToTop()"
                    class="bg-teal-600 text-white p-4 rounded-full shadow-lg hover:bg-teal-700 transition-all duration-300 hover:scale-110 pulse-glow focus:outline-none focus:ring-4 focus:ring-teal-300"
                    aria-label="العودة إلى أعلى الصفحة"
                    title="العودة إلى أعلى الصفحة">
                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24" aria-hidden="true">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 10l7-7m0 0l7 7m-7-7v18"></path>
                </svg>
            </button>
        </div>

        <!-- Skip to main content link for accessibility -->
        <a href="#main-content" class="sr-only focus:not-sr-only focus:absolute focus:top-4 focus:left-4 bg-teal-600 text-white px-4 py-2 rounded-md z-50">
            الانتقال إلى المحتوى الرئيسي
        </a>

    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function () {
            // Enhanced Chart.js implementation
            const ctx = document.getElementById('supplierStatusChart').getContext('2d');
            const supplierStatusChart = new Chart(ctx, {
                type: 'doughnut',
                data: {
                    labels: ['معتمد', 'قيد المراجعة', 'جديد', 'مرفوض'],
                    datasets: [{
                        label: 'حالة الموردين',
                        data: [75, 15, 8, 2],
                        backgroundColor: [
                            'linear-gradient(135deg, #10b981, #059669)',
                            'linear-gradient(135deg, #f59e0b, #d97706)',
                            'linear-gradient(135deg, #3b82f6, #2563eb)',
                            'linear-gradient(135deg, #ef4444, #dc2626)'
                        ],
                        borderColor: '#ffffff',
                        borderWidth: 4,
                        hoverOffset: 15
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    animation: {
                        animateRotate: true,
                        animateScale: true,
                        duration: 2000,
                        easing: 'easeOutBounce'
                    },
                    plugins: {
                        legend: {
                            position: 'bottom',
                            labels: {
                                font: {
                                    family: 'Tajawal, sans-serif',
                                    size: 14
                                },
                                padding: 20,
                                usePointStyle: true,
                                pointStyle: 'circle'
                            }
                        },
                        tooltip: {
                            bodyFont: {
                                family: 'Tajawal, sans-serif'
                            },
                            titleFont: {
                                family: 'Tajawal, sans-serif'
                            },
                            backgroundColor: 'rgba(0, 0, 0, 0.8)',
                            titleColor: '#ffffff',
                            bodyColor: '#ffffff',
                            borderColor: '#0f766e',
                            borderWidth: 2,
                            cornerRadius: 10,
                            displayColors: true
                        },
                        title: {
                            display: true,
                            text: 'توزيع حالات الموردين (مثال تفاعلي)',
                            font: {
                                family: 'Tajawal, sans-serif',
                                size: 18,
                                weight: 'bold'
                            },
                            color: '#0f766e',
                            padding: {
                                top: 10,
                                bottom: 20
                            }
                        }
                    },
                    onHover: (event, activeElements) => {
                        event.native.target.style.cursor = activeElements.length > 0 ? 'pointer' : 'default';
                    }
                }
            });

            // Enhanced UI Mockup Tabs with animations
            const tabButtons = document.querySelectorAll('.tab-btn');
            const tabContents = document.querySelectorAll('.tab-content');

            tabButtons.forEach(button => {
                button.addEventListener('click', () => {
                    const tabId = button.dataset.tab;

                    // Update button styles with animation
                    tabButtons.forEach(btn => {
                        btn.classList.remove('border-teal-500', 'text-teal-600');
                        btn.classList.add('border-transparent', 'text-slate-500', 'hover:text-slate-700', 'hover:border-slate-300');
                    });
                    button.classList.add('border-teal-500', 'text-teal-600');
                    button.classList.remove('border-transparent', 'text-slate-500');

                    // Show/hide tab content with fade effect
                    tabContents.forEach(content => {
                        if (content.id === tabId) {
                            content.classList.remove('hidden');
                            content.style.opacity = '0';
                            setTimeout(() => {
                                content.style.opacity = '1';
                                content.style.transition = 'opacity 0.3s ease-in-out';
                            }, 10);
                        } else {
                            content.classList.add('hidden');
                        }
                    });
                });
            });

            // Enhanced interactions and animations
            addScrollAnimations();
            addTooltips();
            addLoadingStates();
            addModalFunctionality();
        });

        // Scroll animations
        function addScrollAnimations() {
            const observerOptions = {
                threshold: 0.1,
                rootMargin: '0px 0px -50px 0px'
            };

            const observer = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        entry.target.classList.add('fade-in');
                    }
                });
            }, observerOptions);

            document.querySelectorAll('.card-hover, .timeline-item, .glass-effect').forEach(el => {
                observer.observe(el);
            });
        }

        // Enhanced tooltips
        function addTooltips() {
            const tooltip = document.getElementById('tooltip');

            document.querySelectorAll('[data-tooltip]').forEach(element => {
                element.addEventListener('mouseenter', (e) => {
                    const text = e.target.getAttribute('data-tooltip');
                    tooltip.textContent = text;
                    tooltip.style.opacity = '1';
                });

                element.addEventListener('mousemove', (e) => {
                    tooltip.style.left = e.pageX + 10 + 'px';
                    tooltip.style.top = e.pageY - 30 + 'px';
                });

                element.addEventListener('mouseleave', () => {
                    tooltip.style.opacity = '0';
                });
            });
        }

        // Loading states for buttons
        function addLoadingStates() {
            document.querySelectorAll('button').forEach(button => {
                button.addEventListener('click', function() {
                    if (this.textContent.includes('استكشف')) {
                        const spinner = this.querySelector('.loading-spinner');
                        spinner.classList.remove('hidden');
                        this.disabled = true;

                        setTimeout(() => {
                            spinner.classList.add('hidden');
                            this.disabled = false;
                            showModal('تم تحميل النظام بنجاح! يمكنك الآن استكشاف جميع الميزات المتاحة.');
                        }, 2000);
                    }
                });
            });
        }

        // Modal functionality
        function addModalFunctionality() {
            window.showModal = function(content) {
                const modal = document.getElementById('infoModal');
                const modalContent = document.getElementById('modalContent');
                modalContent.textContent = content;
                modal.classList.remove('hidden');
                modal.classList.add('flex');
            };

            window.closeModal = function() {
                const modal = document.getElementById('infoModal');
                modal.classList.add('hidden');
                modal.classList.remove('flex');
            };

            // Close modal on outside click
            document.getElementById('infoModal').addEventListener('click', function(e) {
                if (e.target === this) {
                    closeModal();
                }
            });
        }

        // Scroll to top functionality
        function scrollToTop() {
            window.scrollTo({
                top: 0,
                behavior: 'smooth'
            });
        }

        // Add floating action button visibility
        window.addEventListener('scroll', function() {
            const fab = document.querySelector('.fixed.bottom-6');
            if (window.scrollY > 300) {
                fab.style.opacity = '1';
                fab.style.transform = 'scale(1)';
            } else {
                fab.style.opacity = '0.7';
                fab.style.transform = 'scale(0.8)';
            }
        });

        // Loading screen functionality
        window.addEventListener('load', function() {
            const loadingScreen = document.getElementById('loadingScreen');
            const progressBar = document.getElementById('progressBar');

            // Simulate loading progress
            let progress = 0;
            const interval = setInterval(() => {
                progress += Math.random() * 15;
                if (progress >= 100) {
                    progress = 100;
                    clearInterval(interval);

                    setTimeout(() => {
                        loadingScreen.style.opacity = '0';
                        setTimeout(() => {
                            loadingScreen.style.display = 'none';
                        }, 500);
                    }, 500);
                }
                progressBar.style.width = progress + '%';
            }, 100);
        });

        // Export functionality
        function exportReport() {
            showModal('جاري تحضير التقرير للتصدير... سيتم تحميل ملف PDF يحتوي على جميع بيانات النظام.');

            // Simulate export process
            setTimeout(() => {
                const link = document.createElement('a');
                link.href = 'data:text/plain;charset=utf-8,' + encodeURIComponent('تقرير نظام إدارة الموردين - جمعية المنقف التعاونية\n\nهذا مثال على تقرير مُصدر من النظام.');
                link.download = 'تقرير_نظام_الموردين.txt';
                link.click();
            }, 2000);
        }

        // Print functionality
        function printPage() {
            // Hide non-printable elements
            const nonPrintable = document.querySelectorAll('.fixed, #infoModal, #tooltip');
            nonPrintable.forEach(el => el.style.display = 'none');

            window.print();

            // Restore elements after printing
            setTimeout(() => {
                nonPrintable.forEach(el => el.style.display = '');
            }, 1000);
        }

        // Search and filter functionality
        function applyFilters() {
            const searchTerm = document.getElementById('searchInput').value.toLowerCase();
            const statusFilter = document.getElementById('statusFilter').value;

            showModal(`تم تطبيق الفلترة:\nالبحث: "${searchTerm || 'جميع النتائج'}"\nالحالة: "${getStatusText(statusFilter)}"\n\nعدد النتائج: ${Math.floor(Math.random() * 50) + 10} مورد`);
        }

        function getStatusText(status) {
            const statusMap = {
                'active': 'معتمد',
                'pending': 'قيد المراجعة',
                'new': 'جديد',
                'rejected': 'مرفوض',
                '': 'جميع الحالات'
            };
            return statusMap[status] || 'غير محدد';
        }

        // Enhanced search with real-time suggestions
        document.addEventListener('DOMContentLoaded', function() {
            const searchInput = document.getElementById('searchInput');
            if (searchInput) {
                searchInput.addEventListener('input', function() {
                    const value = this.value;
                    if (value.length > 2) {
                        // Simulate search suggestions
                        console.log('البحث عن:', value);
                    }
                });
            }
        });

        // Keyboard shortcuts
        document.addEventListener('keydown', function(e) {
            // Ctrl+F for search
            if (e.ctrlKey && e.key === 'f') {
                e.preventDefault();
                document.getElementById('searchInput')?.focus();
            }

            // Ctrl+P for print
            if (e.ctrlKey && e.key === 'p') {
                e.preventDefault();
                printPage();
            }

            // Escape to close modal
            if (e.key === 'Escape') {
                closeModal();
            }
        });

        // Performance monitoring
        if ('performance' in window) {
            window.addEventListener('load', function() {
                setTimeout(() => {
                    const perfData = performance.getEntriesByType('navigation')[0];
                    console.log('وقت التحميل:', Math.round(perfData.loadEventEnd - perfData.fetchStart), 'ms');
                }, 0);
            });
        }
    </script>

</body>
</html>