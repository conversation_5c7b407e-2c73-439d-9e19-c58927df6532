<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>خطة عمل تفاعلية | نظام إدارة الموردين</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Tajawal:wght@400;500;700&display=swap" rel="stylesheet">

    <!-- Chosen Palette: Calm Harmony (Neutral slates with a soft teal accent) -->
    <!-- Application Structure Plan: A single-page, top-to-bottom dashboard layout was chosen to present the C# application plan as a narrative story. It starts with the high-level goal, dives into the technical architecture (database, UI), shows the implementation plan, and concludes with a sample data visualization. This linear flow is intuitive for understanding a project plan, while interactive elements in each section encourage exploration of the details without overwhelming the user. -->
    <!-- Visualization & Content Choices: 
        - Report's Objectives -> Goal: Inform -> Viz: List with icons -> Interaction: None -> Justification: Clear, scannable introduction to the project's purpose.
        - System Architecture -> Goal: Organize -> Viz: HTML/Tailwind diagram -> Interaction: Hover effects -> Justification: Visually clarifies the relationship between system components better than text.
        - Report's DB Schema -> Goal: Organize/Inform -> Viz: Interactive HTML cards -> Interaction: Hover effects to lift cards -> Justification: Breaks down complex table structures into digestible, visually appealing components.
        - Report's UI Design -> Goal: Visualize -> Viz: Simple HTML mockups -> Interaction: Clickable tabs to switch views -> Justification: Provides a concrete, interactive preview of the proposed user interface flow.
        - Execution Plan -> Goal: Show Process -> Viz: Vertical timeline -> Interaction: None -> Justification: A standard and highly readable format for presenting phased project steps.
        - Reporting Goal -> Goal: Compare/Inform -> Viz: Donut Chart (Chart.js) -> Interaction: Hover for tooltips -> Justification: Demonstrates a key output of the proposed system (reporting) and adds a dynamic data visualization element.
    -->
    <!-- CONFIRMATION: NO SVG graphics used. NO Mermaid JS used. -->

    <style>
        body {
            font-family: 'Tajawal', sans-serif;
        }
        .chart-container {
            position: relative;
            width: 100%;
            max-width: 400px;
            height: 400px;
            margin: auto;
        }
        .timeline-item::before {
            content: '';
            position: absolute;
            top: 1rem;
            right: -0.8rem;
            width: 1.5rem;
            height: 1.5rem;
            background-color: white;
            border: 4px solid #0d9488;
            border-radius: 50%;
            z-index: 10;
        }
    </style>
</head>
<body class="bg-slate-50 text-slate-800">

    <div class="container mx-auto p-4 sm:p-6 lg:p-8">

        <!-- Section 1: Header -->
        <header class="text-center py-8">
            <h1 class="text-3xl md:text-5xl font-bold text-teal-700">نظام إدارة واعتماد الموردين</h1>
            <p class="mt-4 text-lg text-slate-600">خطة عمل تفاعلية لتطوير نظام لجمعية المنقف التعاونية</p>
        </header>

        <main>
            <!-- Section 2: Objectives -->
            <section id="objectives" class="my-12">
                <h2 class="text-2xl font-bold text-center mb-8">🎯 أهداف النظام الرئيسية</h2>
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                    <div class="bg-white p-6 rounded-xl shadow-md hover:shadow-lg transition-shadow">
                        <h3 class="font-bold text-lg text-teal-700">مركزية البيانات</h3>
                        <p class="mt-2 text-slate-600">إنشاء قاعدة بيانات موحدة لكافة الموردين وبياناتهم الرسمية ومستنداتهم.</p>
                    </div>
                    <div class="bg-white p-6 rounded-xl shadow-md hover:shadow-lg transition-shadow">
                        <h3 class="font-bold text-lg text-teal-700">أتمتة الاعتماد</h3>
                        <p class="mt-2 text-slate-600">تتبع حالة اعتماد كل مورد (جديد، قيد المراجعة، معتمد، مرفوض) بكفاءة.</p>
                    </div>
                    <div class="bg-white p-6 rounded-xl shadow-md hover:shadow-lg transition-shadow">
                        <h3 class="font-bold text-lg text-teal-700">إدارة الوثائق</h3>
                        <p class="mt-2 text-slate-600">أرشفة المستندات وتتبع تواريخ صلاحيتها لتجنب أي مشاكل قانونية.</p>
                    </div>
                </div>
            </section>
            
            <hr class="my-12 border-slate-200">

            <!-- Section 3: Architecture -->
            <section id="architecture" class="my-12">
                <h2 class="text-2xl font-bold text-center mb-12">🏗️ البنية التقنية المقترحة</h2>
                 <p class="text-center text-slate-600 mb-8 max-w-2xl mx-auto">
                    تم تصميم النظام ليكون تطبيقًا مكتبيًا قويًا باستخدام تقنيات مايكروسوفت الموثوقة لضمان الأداء العالي والأمان. يتكون النظام من ثلاث طبقات رئيسية تعمل معًا لتقديم تجربة مستخدم سلسة.
                </p>
                <div class="flex flex-col md:flex-row items-center justify-center gap-4 text-center">
                    <div class="bg-white p-6 rounded-xl shadow-lg w-full md:w-1/4 transition-transform hover:scale-105">
                        <h3 class="text-xl font-bold text-teal-700">قاعدة البيانات</h3>
                        <p class="font-mono text-teal-900 mt-2">SQL Server</p>
                        <p class="text-sm text-slate-500 mt-2">لتخزين جميع البيانات بشكل آمن ومنظم.</p>
                    </div>
                    <div class="text-4xl text-teal-500 transform md:-rotate-90">➔</div>
                    <div class="bg-white p-6 rounded-xl shadow-lg w-full md:w-1/4 transition-transform hover:scale-105">
                        <h3 class="text-xl font-bold text-teal-700">منطق الأعمال</h3>
                        <p class="font-mono text-teal-900 mt-2">C# (.NET)</p>
                        <p class="text-sm text-slate-500 mt-2">لمعالجة البيانات وتنفيذ كافة العمليات.</p>
                    </div>
                     <div class="text-4xl text-teal-500 transform md:-rotate-90">➔</div>
                    <div class="bg-white p-6 rounded-xl shadow-lg w-full md:w-1/4 transition-transform hover:scale-105">
                        <h3 class="text-xl font-bold text-teal-700">واجهة المستخدم</h3>
                        <p class="font-mono text-teal-900 mt-2">WPF / MAUI</p>
                        <p class="text-sm text-slate-500 mt-2">واجهات رسومية سهلة الاستخدام للموظفين.</p>
                    </div>
                </div>
            </section>

            <hr class="my-12 border-slate-200">

            <!-- Section 4: Database Schema -->
            <section id="database" class="my-12">
                <h2 class="text-2xl font-bold text-center mb-8">🗃️ تصميم قاعدة البيانات</h2>
                <p class="text-center text-slate-600 mb-8 max-w-2xl mx-auto">
                    تمثل قاعدة البيانات قلب النظام. فيما يلي نظرة على الجداول الرئيسية المقترحة وكيفية ارتباطها ببعضها البعض لتخزين معلومات الموردين ووثائقهم وسجلات اعتمادهم.
                </p>
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                    <!-- Supplier Card -->
                    <div class="bg-white p-6 rounded-xl shadow-lg border-t-4 border-teal-500 transition-all duration-300 hover:shadow-2xl hover:-translate-y-2">
                        <h3 class="text-xl font-bold mb-4">جدول الموردين (Suppliers)</h3>
                        <ul class="space-y-2 text-slate-700">
                            <li class="font-bold">SupplierID (PK)</li>
                            <li>SupplierName</li>
                            <li>CommercialName</li>
                            <li>LicenseNumber</li>
                            <li>ContactPerson</li>
                            <li>PhoneNumber, Email</li>
                            <li>Status</li>
                        </ul>
                    </div>
                    <!-- Documents Card -->
                    <div class="bg-white p-6 rounded-xl shadow-lg border-t-4 border-amber-500 transition-all duration-300 hover:shadow-2xl hover:-translate-y-2">
                        <h3 class="text-xl font-bold mb-4">وثائق الموردين (Documents)</h3>
                        <ul class="space-y-2 text-slate-700">
                            <li class="font-bold">DocumentID (PK)</li>
                            <li class="font-semibold text-teal-700">SupplierID (FK)</li>
                            <li>DocumentType</li>
                            <li>IssueDate</li>
                            <li>ExpiryDate</li>
                            <li>FilePath</li>
                        </ul>
                    </div>
                    <!-- Log Card -->
                    <div class="bg-white p-6 rounded-xl shadow-lg border-t-4 border-sky-500 transition-all duration-300 hover:shadow-2xl hover:-translate-y-2">
                        <h3 class="text-xl font-bold mb-4">سجل الاعتماد (Accreditation)</h3>
                        <ul class="space-y-2 text-slate-700">
                            <li class="font-bold">LogID (PK)</li>
                            <li class="font-semibold text-teal-700">SupplierID (FK)</li>
                            <li>ReviewDate</li>
                            <li>AccreditationStatus</li>
                            <li>ReviewerName</li>
                            <li>Notes</li>
                        </ul>
                    </div>
                </div>
            </section>
            
            <hr class="my-12 border-slate-200">

            <!-- Section 5: UI Mockup -->
            <section id="ui-mockup" class="my-12">
                <h2 class="text-2xl font-bold text-center mb-8">🖥️ تصور واجهات المستخدم</h2>
                 <p class="text-center text-slate-600 mb-8 max-w-2xl mx-auto">
                    الهدف هو تصميم واجهات مستخدم نظيفة وبسيطة. هذا النموذج التفاعلي يوضح الشاشة الرئيسية لإدارة بيانات أحد الموردين، مع تقسيم المعلومات إلى تبويبات لسهولة الوصول.
                </p>
                <div class="max-w-4xl mx-auto bg-white rounded-xl shadow-2xl overflow-hidden border border-slate-200">
                    <div class="bg-slate-100 p-4 border-b border-slate-200 flex justify-between items-center">
                        <h3 class="font-bold text-lg">تفاصيل المورد: شركة التوريدات المتحدة</h3>
                        <div class="flex space-x-2 space-x-reverse">
                            <span class="w-3 h-3 bg-red-400 rounded-full"></span>
                            <span class="w-3 h-3 bg-yellow-400 rounded-full"></span>
                            <span class="w-3 h-3 bg-green-400 rounded-full"></span>
                        </div>
                    </div>
                    <div class="p-6">
                        <div class="border-b border-slate-200">
                            <nav class="flex -mb-px space-x-6 space-x-reverse" id="tabs-nav">
                                <button data-tab="tab1" class="tab-btn whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm border-teal-500 text-teal-600">المعلومات الأساسية</button>
                                <button data-tab="tab2" class="tab-btn whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm border-transparent text-slate-500 hover:text-slate-700 hover:border-slate-300">الوثائق والمستندات</button>
                                <button data-tab="tab3" class="tab-btn whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm border-transparent text-slate-500 hover:text-slate-700 hover:border-slate-300">سجل الاعتماد</button>
                            </nav>
                        </div>
                        <div id="tabs-content" class="mt-6">
                            <div id="tab1" class="tab-content grid grid-cols-1 md:grid-cols-2 gap-6">
                                <div><label class="font-semibold">الاسم التجاري:</label><p class="text-slate-600">شركة التوريدات المتحدة</p></div>
                                <div><label class="font-semibold">رقم الرخصة:</label><p class="text-slate-600">12345/2023</p></div>
                                <div><label class="font-semibold">مسؤول التواصل:</label><p class="text-slate-600">السيد/ عبدالله الفهد</p></div>
                                <div><label class="font-semibold">الهاتف:</label><p class="text-slate-600">98765432</p></div>
                                <div class="md:col-span-2"><label class="font-semibold">العنوان:</label><p class="text-slate-600">المنقف، قطعة 1، شارع 10، مبنى 50</p></div>
                            </div>
                             <div id="tab2" class="tab-content hidden">
                                <table class="w-full text-sm text-left text-gray-500">
                                    <thead class="text-xs text-gray-700 uppercase bg-gray-50">
                                        <tr>
                                            <th scope="col" class="px-6 py-3">نوع الوثيقة</th>
                                            <th scope="col" class="px-6 py-3">تاريخ الإصدار</th>
                                            <th scope="col" class="px-6 py-3">تاريخ الانتهاء</th>
                                            <th scope="col" class="px-6 py-3">الحالة</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr class="bg-white border-b">
                                            <td class="px-6 py-4">الرخصة التجارية</td>
                                            <td class="px-6 py-4">01/01/2024</td>
                                            <td class="px-6 py-4">31/12/2025</td>
                                            <td class="px-6 py-4"><span class="bg-green-100 text-green-800 text-xs font-medium mr-2 px-2.5 py-0.5 rounded-full">سارية</span></td>
                                        </tr>
                                        <tr class="bg-white border-b">
                                            <td class="px-6 py-4">شهادة الملاءة المالية</td>
                                            <td class="px-6 py-4">15/06/2024</td>
                                            <td class="px-6 py-4">14/06/2025</td>
                                            <td class="px-6 py-4"><span class="bg-green-100 text-green-800 text-xs font-medium mr-2 px-2.5 py-0.5 rounded-full">سارية</span></td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                            <div id="tab3" class="tab-content hidden">
                               <ul class="space-y-4">
                                   <li class="p-3 bg-green-50 rounded-lg"><strong>25/05/2024 - معتمد:</strong> تمت الموافقة على جميع المستندات. المراجع: أحمد الصالح.</li>
                                   <li class="p-3 bg-yellow-50 rounded-lg"><strong>20/05/2024 - قيد المراجعة:</strong> تم استلام الطلب والمستندات. المراجع: أحمد الصالح.</li>
                               </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </section>
            
            <hr class="my-12 border-slate-200">

            <!-- Section 6: Plan & Chart -->
            <section id="plan" class="my-12">
                <div class="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
                    <div>
                        <h2 class="text-2xl font-bold mb-8 text-center lg:text-right">🗺️ خطة التنفيذ والتقارير</h2>
                        <p class="text-slate-600 mb-8 text-center lg:text-right">
                           سيتم تنفيذ المشروع على مراحل واضحة لضمان الجودة والتسليم في الوقت المناسب. أحد أهم مخرجات النظام هو توفير تقارير إحصائية دقيقة، مثل هذا الرسم البياني الذي يوضح حالة الموردين الحالية.
                        </p>
                        <div class="relative border-r-4 border-teal-600 pr-8 space-y-12">
                            <div class="timeline-item">
                                <h4 class="font-bold text-lg">المرحلة 1: الإعداد والبنية التحتية</h4>
                                <p class="text-sm text-slate-500">تثبيت الأدوات وإنشاء قاعدة البيانات.</p>
                            </div>
                            <div class="timeline-item">
                                <h4 class="font-bold text-lg">المرحلة 2: تطوير منطق البيانات</h4>
                                <p class="text-sm text-slate-500">كتابة كود C# للاتصال بقاعدة البيانات.</p>
                            </div>
                            <div class="timeline-item">
                                <h4 class="font-bold text-lg">المرحلة 3: بناء الواجهات</h4>
                                <p class="text-sm text-slate-500">تصميم وتنفيذ الشاشات والنماذج.</p>
                            </div>
                             <div class="timeline-item">
                                <h4 class="font-bold text-lg">المرحلة 4: الربط والاختبار</h4>
                                <p class="text-sm text-slate-500">ربط الواجهات بالمنطق البرمجي واختبار النظام.</p>
                            </div>
                             <div class="timeline-item">
                                <h4 class="font-bold text-lg">المرحلة 5: النشر والتدريب</h4>
                                <p class="text-sm text-slate-500">تثبيت البرنامج وتدريب الموظفين.</p>
                            </div>
                        </div>
                    </div>

                    <div class="w-full">
                         <div class="chart-container">
                            <canvas id="supplierStatusChart"></canvas>
                        </div>
                    </div>
                </div>
            </section>

        </main>
        
        <footer class="text-center py-8 mt-12 border-t border-slate-200">
            <p class="text-slate-500">تصور تفاعلي لخطة عمل مشروع جمعية المنقف التعاونية</p>
        </footer>

    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function () {
            // Chart.js implementation
            const ctx = document.getElementById('supplierStatusChart').getContext('2d');
            const supplierStatusChart = new Chart(ctx, {
                type: 'doughnut',
                data: {
                    labels: ['معتمد', 'قيد المراجعة', 'جديد', 'مرفوض'],
                    datasets: [{
                        label: 'حالة الموردين',
                        data: [75, 15, 8, 2],
                        backgroundColor: [
                            '#0d9488', // teal-600
                            '#f59e0b', // amber-500
                            '#38bdf8', // sky-400
                            '#f87171'  // red-400
                        ],
                        borderColor: '#ffffff',
                        borderWidth: 4
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: 'bottom',
                            labels: {
                                font: {
                                    family: 'Tajawal, sans-serif',
                                    size: 14
                                }
                            }
                        },
                        tooltip: {
                            bodyFont: {
                                family: 'Tajawal, sans-serif'
                            },
                            titleFont: {
                                family: 'Tajawal, sans-serif'
                            }
                        },
                        title: {
                            display: true,
                            text: 'توزيع حالات الموردين (مثال)',
                            font: {
                                family: 'Tajawal, sans-serif',
                                size: 18,
                                weight: 'bold'
                            },
                            padding: {
                                top: 10,
                                bottom: 20
                            }
                        }
                    }
                }
            });

            // UI Mockup Tabs
            const tabButtons = document.querySelectorAll('.tab-btn');
            const tabContents = document.querySelectorAll('.tab-content');

            tabButtons.forEach(button => {
                button.addEventListener('click', () => {
                    const tabId = button.dataset.tab;

                    // Update button styles
                    tabButtons.forEach(btn => {
                        btn.classList.remove('border-teal-500', 'text-teal-600');
                        btn.classList.add('border-transparent', 'text-slate-500', 'hover:text-slate-700', 'hover:border-slate-300');
                    });
                    button.classList.add('border-teal-500', 'text-teal-600');
                    button.classList.remove('border-transparent', 'text-slate-500');

                    // Show/hide tab content
                    tabContents.forEach(content => {
                        if (content.id === tabId) {
                            content.classList.remove('hidden');
                        } else {
                            content.classList.add('hidden');
                        }
                    });
                });
            });
        });
    </script>

</body>
</html>