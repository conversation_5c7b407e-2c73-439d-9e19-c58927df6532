using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.IO;

namespace SupplierManagementSystem.Models
{
    /// <summary>
    /// نموذج بيانات وثائق المورد
    /// Supplier Document Data Model
    /// </summary>
    public class SupplierDocument
    {
        [Key]
        public int DocumentID { get; set; }

        [Required]
        [Display(Name = "معرف المورد")]
        public int SupplierID { get; set; }

        [Required]
        [Display(Name = "نوع الوثيقة")]
        public int DocumentTypeID { get; set; }

        [Required(ErrorMessage = "اسم الوثيقة مطلوب")]
        [StringLength(200, ErrorMessage = "اسم الوثيقة يجب أن يكون أقل من 200 حرف")]
        [Display(Name = "اسم الوثيقة")]
        public string DocumentName { get; set; } = string.Empty;

        [StringLength(500)]
        [Display(Name = "مسار الملف")]
        public string? FilePath { get; set; }

        [Display(Name = "حجم الملف")]
        public long? FileSize { get; set; }

        [StringLength(10)]
        [Display(Name = "امتداد الملف")]
        public string? FileExtension { get; set; }

        [Display(Name = "تاريخ الإصدار")]
        [DataType(DataType.Date)]
        public DateTime? IssueDate { get; set; }

        [Display(Name = "تاريخ الانتهاء")]
        [DataType(DataType.Date)]
        public DateTime? ExpiryDate { get; set; }

        [Required]
        [StringLength(20)]
        [Display(Name = "الحالة")]
        public string Status { get; set; } = "سارية";

        [Display(Name = "تاريخ الرفع")]
        public DateTime UploadedDate { get; set; } = DateTime.Now;

        [StringLength(100)]
        [Display(Name = "رُفع بواسطة")]
        public string? UploadedBy { get; set; }

        [Display(Name = "نشط")]
        public bool IsActive { get; set; } = true;

        [StringLength(500)]
        [Display(Name = "ملاحظات")]
        public string? Notes { get; set; }

        // Navigation Properties
        [ForeignKey("SupplierID")]
        public virtual Supplier? Supplier { get; set; }

        [ForeignKey("DocumentTypeID")]
        public virtual DocumentType? DocumentType { get; set; }

        // Computed Properties
        [NotMapped]
        [Display(Name = "حجم الملف (MB)")]
        public decimal? FileSizeMB => FileSize.HasValue ? Math.Round((decimal)FileSize.Value / (1024 * 1024), 2) : null;

        [NotMapped]
        [Display(Name = "الأيام المتبقية")]
        public int? DaysUntilExpiry => ExpiryDate.HasValue ? (int)(ExpiryDate.Value - DateTime.Today).TotalDays : null;

        [NotMapped]
        [Display(Name = "منتهية الصلاحية")]
        public bool IsExpired => ExpiryDate.HasValue && ExpiryDate.Value < DateTime.Today;

        [NotMapped]
        [Display(Name = "تنتهي قريباً")]
        public bool IsExpiringSoon => DaysUntilExpiry.HasValue && DaysUntilExpiry.Value <= 30 && DaysUntilExpiry.Value >= 0;

        [NotMapped]
        [Display(Name = "لون الحالة")]
        public string StatusColor => Status switch
        {
            "سارية" => IsExpired ? "#ef4444" : IsExpiringSoon ? "#f59e0b" : "#10b981",
            "منتهية الصلاحية" => "#ef4444",
            "قيد التجديد" => "#f59e0b",
            "مرفوضة" => "#ef4444",
            _ => "#6b7280"
        };

        [NotMapped]
        [Display(Name = "أيقونة الحالة")]
        public string StatusIcon => Status switch
        {
            "سارية" => IsExpired ? "❌" : IsExpiringSoon ? "⚠️" : "✅",
            "منتهية الصلاحية" => "❌",
            "قيد التجديد" => "🔄",
            "مرفوضة" => "❌",
            _ => "❓"
        };

        [NotMapped]
        [Display(Name = "اسم الملف")]
        public string? FileName => !string.IsNullOrEmpty(FilePath) ? Path.GetFileName(FilePath) : null;

        [NotMapped]
        [Display(Name = "يوجد ملف")]
        public bool HasFile => !string.IsNullOrEmpty(FilePath) && File.Exists(FilePath);

        // Methods
        public void UpdateStatus()
        {
            if (IsExpired)
            {
                Status = "منتهية الصلاحية";
            }
            else if (IsExpiringSoon)
            {
                // يمكن إبقاء الحالة كما هي أو تغييرها حسب الحاجة
                // Keep status as is or change based on business rules
            }
        }

        public bool IsValidForApproval()
        {
            return IsActive && !IsExpired && HasFile;
        }

        public string GetFormattedFileSize()
        {
            if (!FileSize.HasValue) return "غير محدد";

            var size = FileSize.Value;
            string[] sizes = { "B", "KB", "MB", "GB" };
            int order = 0;
            while (size >= 1024 && order < sizes.Length - 1)
            {
                order++;
                size = size / 1024;
            }

            return $"{size:0.##} {sizes[order]}";
        }

        public override string ToString()
        {
            return $"{DocumentName} - {Status}";
        }
    }

    /// <summary>
    /// تعداد حالات الوثيقة
    /// Document Status Enumeration
    /// </summary>
    public static class DocumentStatus
    {
        public const string Valid = "سارية";
        public const string Expired = "منتهية الصلاحية";
        public const string Renewing = "قيد التجديد";
        public const string Rejected = "مرفوضة";

        public static readonly string[] AllStatuses = { Valid, Expired, Renewing, Rejected };

        public static string GetDisplayName(string status)
        {
            return status switch
            {
                Valid => "سارية",
                Expired => "منتهية الصلاحية",
                Renewing => "قيد التجديد",
                Rejected => "مرفوضة",
                _ => "غير محدد"
            };
        }
    }
}
