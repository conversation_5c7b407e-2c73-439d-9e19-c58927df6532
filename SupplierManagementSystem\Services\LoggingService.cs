using System;
using System.IO;
using System.Threading.Tasks;

namespace SupplierManagementSystem.Services;

/// <summary>
/// خدمة تسجيل الأحداث والأخطاء
/// Logging Service for Events and Errors
/// </summary>
public class LoggingService
{
    private readonly string _logDirectory;
    private readonly string _logFileName;
    private static readonly object _lockObject = new object();

    public LoggingService()
    {
        _logDirectory = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "Logs");
        _logFileName = Path.Combine(_logDirectory, $"SupplierSystem_{DateTime.Now:yyyyMMdd}.log");
        
        // إنشاء مجلد السجلات إذا لم يكن موجوداً
        if (!Directory.Exists(_logDirectory))
        {
            Directory.CreateDirectory(_logDirectory);
        }
    }

    public enum LogLevel
    {
        Info,
        Warning,
        Error,
        Debug,
        Critical
    }

    public void LogInfo(string message, string source = "")
    {
        Log(LogLevel.Info, message, source);
    }

    public void LogWarning(string message, string source = "")
    {
        Log(LogLevel.Warning, message, source);
    }

    public void LogError(string message, Exception exception = null, string source = "")
    {
        var fullMessage = exception != null 
            ? $"{message} | Exception: {exception.Message} | StackTrace: {exception.StackTrace}"
            : message;
        
        Log(LogLevel.Error, fullMessage, source);
    }

    public void LogDebug(string message, string source = "")
    {
        #if DEBUG
        Log(LogLevel.Debug, message, source);
        #endif
    }

    public void LogCritical(string message, Exception exception = null, string source = "")
    {
        var fullMessage = exception != null 
            ? $"{message} | Exception: {exception.Message} | StackTrace: {exception.StackTrace}"
            : message;
        
        Log(LogLevel.Critical, fullMessage, source);
    }

    private void Log(LogLevel level, string message, string source)
    {
        try
        {
            var timestamp = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss.fff");
            var logEntry = $"[{timestamp}] [{level}] [{source}] {message}";

            lock (_lockObject)
            {
                File.AppendAllText(_logFileName, logEntry + Environment.NewLine);
            }

            // طباعة في وحدة التحكم أيضاً في وضع التطوير
            #if DEBUG
            Console.WriteLine(logEntry);
            System.Diagnostics.Debug.WriteLine(logEntry);
            #endif
        }
        catch (Exception ex)
        {
            // في حالة فشل الكتابة في الملف، اطبع في وحدة التحكم
            System.Diagnostics.Debug.WriteLine($"فشل في كتابة السجل: {ex.Message}");
        }
    }

    public async Task LogAsync(LogLevel level, string message, string source = "")
    {
        await Task.Run(() => Log(level, message, source));
    }

    public void CleanOldLogs(int daysToKeep = 30)
    {
        try
        {
            var cutoffDate = DateTime.Now.AddDays(-daysToKeep);
            var logFiles = Directory.GetFiles(_logDirectory, "SupplierSystem_*.log");

            foreach (var file in logFiles)
            {
                var fileInfo = new FileInfo(file);
                if (fileInfo.CreationTime < cutoffDate)
                {
                    File.Delete(file);
                    LogInfo($"تم حذف ملف السجل القديم: {fileInfo.Name}", "LoggingService");
                }
            }
        }
        catch (Exception ex)
        {
            LogError("فشل في تنظيف ملفات السجل القديمة", ex, "LoggingService");
        }
    }

    public string GetLogFilePath()
    {
        return _logFileName;
    }

    public string[] GetRecentLogs(int lineCount = 100)
    {
        try
        {
            if (!File.Exists(_logFileName))
                return new string[0];

            var lines = File.ReadAllLines(_logFileName);
            var startIndex = Math.Max(0, lines.Length - lineCount);
            var recentLines = new string[Math.Min(lineCount, lines.Length)];
            
            Array.Copy(lines, startIndex, recentLines, 0, recentLines.Length);
            return recentLines;
        }
        catch (Exception ex)
        {
            LogError("فشل في قراءة السجلات الحديثة", ex, "LoggingService");
            return new string[] { $"خطأ في قراءة السجلات: {ex.Message}" };
        }
    }
}
