using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;

namespace SupplierManagementSystem.Models
{
    /// <summary>
    /// نموذج بيانات أنواع الوثائق
    /// Document Type Data Model
    /// </summary>
    public class DocumentType
    {
        [Key]
        public int DocumentTypeID { get; set; }

        [Required(ErrorMessage = "اسم نوع الوثيقة مطلوب")]
        [StringLength(100, ErrorMessage = "اسم نوع الوثيقة يجب أن يكون أقل من 100 حرف")]
        [Display(Name = "اسم نوع الوثيقة")]
        public string TypeName { get; set; } = string.Empty;

        [StringLength(500, ErrorMessage = "الوصف يجب أن يكون أقل من 500 حرف")]
        [Display(Name = "الوصف")]
        public string? Description { get; set; }

        [Display(Name = "مطلوب")]
        public bool IsRequired { get; set; } = false;

        [Display(Name = "فترة الصلاحية (بالأشهر)")]
        [Range(1, 120, ErrorMessage = "فترة الصلاحية يجب أن تكون بين 1 و 120 شهر")]
        public int ValidityPeriodMonths { get; set; } = 12;

        [Display(Name = "نشط")]
        public bool IsActive { get; set; } = true;

        // Navigation Properties
        public virtual ICollection<SupplierDocument> SupplierDocuments { get; set; } = new List<SupplierDocument>();

        // Methods
        public DateTime CalculateExpiryDate(DateTime issueDate)
        {
            return issueDate.AddMonths(ValidityPeriodMonths);
        }

        public override string ToString()
        {
            return TypeName;
        }
    }
}
