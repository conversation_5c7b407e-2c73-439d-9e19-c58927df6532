-- إنشاء قاعدة بيانات نظام إدارة الموردين لجمعية المنقف التعاونية
-- Supplier Management System Database for Al-Mangaf Cooperative Society

USE master;
GO

-- إنشاء قاعدة البيانات
IF NOT EXISTS (SELECT name FROM sys.databases WHERE name = N'SupplierManagementDB')
BEGIN
    CREATE DATABASE SupplierManagementDB
    COLLATE Arabic_CI_AS;
END
GO

USE SupplierManagementDB;
GO

-- جدول الموردين الرئيسي
-- Main Suppliers Table
CREATE TABLE Suppliers (
    SupplierID INT IDENTITY(1,1) PRIMARY KEY,
    SupplierName NVARCHAR(200) NOT NULL,
    CommercialName NVARCHAR(200),
    LicenseNumber NVARCHAR(50) UNIQUE NOT NULL,
    ContactPerson NVARCHAR(100),
    PhoneNumber NVARCHAR(20),
    Email NVARCHAR(100),
    Address NVARCHAR(500),
    Status NVARCHAR(20) DEFAULT 'جديد' CHECK (Status IN ('جديد', 'قيد المراجعة', 'معتمد', 'مرفوض', 'معلق')),
    CreatedDate DATETIME2 DEFAULT GETDATE(),
    ModifiedDate DATETIME2 DEFAULT GETDATE(),
    CreatedBy NVARCHAR(100),
    ModifiedBy NVARCHAR(100),
    IsActive BIT DEFAULT 1,
    Notes NVARCHAR(1000)
);

-- جدول أنواع الوثائق
-- Document Types Table
CREATE TABLE DocumentTypes (
    DocumentTypeID INT IDENTITY(1,1) PRIMARY KEY,
    TypeName NVARCHAR(100) NOT NULL,
    Description NVARCHAR(500),
    IsRequired BIT DEFAULT 0,
    ValidityPeriodMonths INT DEFAULT 12,
    IsActive BIT DEFAULT 1
);

-- جدول وثائق الموردين
-- Supplier Documents Table
CREATE TABLE SupplierDocuments (
    DocumentID INT IDENTITY(1,1) PRIMARY KEY,
    SupplierID INT NOT NULL,
    DocumentTypeID INT NOT NULL,
    DocumentName NVARCHAR(200) NOT NULL,
    FilePath NVARCHAR(500),
    FileSize BIGINT,
    FileExtension NVARCHAR(10),
    IssueDate DATE,
    ExpiryDate DATE,
    Status NVARCHAR(20) DEFAULT 'سارية' CHECK (Status IN ('سارية', 'منتهية الصلاحية', 'قيد التجديد', 'مرفوضة')),
    UploadedDate DATETIME2 DEFAULT GETDATE(),
    UploadedBy NVARCHAR(100),
    IsActive BIT DEFAULT 1,
    Notes NVARCHAR(500),
    FOREIGN KEY (SupplierID) REFERENCES Suppliers(SupplierID) ON DELETE CASCADE,
    FOREIGN KEY (DocumentTypeID) REFERENCES DocumentTypes(DocumentTypeID)
);

-- جدول لجنة المشتريات
-- Procurement Committee Table
CREATE TABLE ProcurementCommittee (
    CommitteeID INT IDENTITY(1,1) PRIMARY KEY,
    MemberName NVARCHAR(100) NOT NULL,
    Position NVARCHAR(100),
    Email NVARCHAR(100),
    PhoneNumber NVARCHAR(20),
    IsChairman BIT DEFAULT 0,
    IsActive BIT DEFAULT 1,
    JoinDate DATE DEFAULT GETDATE(),
    Notes NVARCHAR(500)
);

-- جدول سجلات الاعتماد
-- Accreditation Records Table
CREATE TABLE AccreditationRecords (
    RecordID INT IDENTITY(1,1) PRIMARY KEY,
    SupplierID INT NOT NULL,
    ReviewDate DATETIME2 DEFAULT GETDATE(),
    ReviewerID INT,
    PreviousStatus NVARCHAR(20),
    NewStatus NVARCHAR(20) CHECK (NewStatus IN ('جديد', 'قيد المراجعة', 'معتمد', 'مرفوض', 'معلق')),
    Decision NVARCHAR(20) CHECK (Decision IN ('موافقة', 'رفض', 'تأجيل', 'طلب مستندات إضافية')),
    ReviewNotes NVARCHAR(1000),
    CommitteeDecision NVARCHAR(1000),
    NextReviewDate DATE,
    IsActive BIT DEFAULT 1,
    FOREIGN KEY (SupplierID) REFERENCES Suppliers(SupplierID) ON DELETE CASCADE,
    FOREIGN KEY (ReviewerID) REFERENCES ProcurementCommittee(CommitteeID)
);

-- جدول تقييم الموردين
-- Supplier Evaluation Table
CREATE TABLE SupplierEvaluations (
    EvaluationID INT IDENTITY(1,1) PRIMARY KEY,
    SupplierID INT NOT NULL,
    EvaluationDate DATETIME2 DEFAULT GETDATE(),
    EvaluatorID INT,
    QualityScore DECIMAL(3,2) CHECK (QualityScore >= 0 AND QualityScore <= 10),
    DeliveryScore DECIMAL(3,2) CHECK (DeliveryScore >= 0 AND DeliveryScore <= 10),
    ServiceScore DECIMAL(3,2) CHECK (ServiceScore >= 0 AND ServiceScore <= 10),
    PriceScore DECIMAL(3,2) CHECK (PriceScore >= 0 AND PriceScore <= 10),
    OverallScore AS ((QualityScore + DeliveryScore + ServiceScore + PriceScore) / 4),
    Comments NVARCHAR(1000),
    Recommendations NVARCHAR(1000),
    IsActive BIT DEFAULT 1,
    FOREIGN KEY (SupplierID) REFERENCES Suppliers(SupplierID) ON DELETE CASCADE,
    FOREIGN KEY (EvaluatorID) REFERENCES ProcurementCommittee(CommitteeID)
);

-- جدول فئات الموردين
-- Supplier Categories Table
CREATE TABLE SupplierCategories (
    CategoryID INT IDENTITY(1,1) PRIMARY KEY,
    CategoryName NVARCHAR(100) NOT NULL,
    Description NVARCHAR(500),
    IsActive BIT DEFAULT 1
);

-- جدول ربط الموردين بالفئات
-- Supplier Category Mapping Table
CREATE TABLE SupplierCategoryMapping (
    MappingID INT IDENTITY(1,1) PRIMARY KEY,
    SupplierID INT NOT NULL,
    CategoryID INT NOT NULL,
    AssignedDate DATETIME2 DEFAULT GETDATE(),
    AssignedBy NVARCHAR(100),
    IsActive BIT DEFAULT 1,
    FOREIGN KEY (SupplierID) REFERENCES Suppliers(SupplierID) ON DELETE CASCADE,
    FOREIGN KEY (CategoryID) REFERENCES SupplierCategories(CategoryID)
);

-- جدول سجل النشاطات
-- Activity Log Table
CREATE TABLE ActivityLog (
    LogID INT IDENTITY(1,1) PRIMARY KEY,
    SupplierID INT,
    ActivityType NVARCHAR(50) NOT NULL,
    ActivityDescription NVARCHAR(500) NOT NULL,
    PerformedBy NVARCHAR(100) NOT NULL,
    PerformedDate DATETIME2 DEFAULT GETDATE(),
    IPAddress NVARCHAR(45),
    UserAgent NVARCHAR(500),
    FOREIGN KEY (SupplierID) REFERENCES Suppliers(SupplierID)
);

-- إنشاء الفهارس لتحسين الأداء
-- Create Indexes for Performance
CREATE INDEX IX_Suppliers_Status ON Suppliers(Status);
CREATE INDEX IX_Suppliers_LicenseNumber ON Suppliers(LicenseNumber);
CREATE INDEX IX_SupplierDocuments_SupplierID ON SupplierDocuments(SupplierID);
CREATE INDEX IX_SupplierDocuments_ExpiryDate ON SupplierDocuments(ExpiryDate);
CREATE INDEX IX_AccreditationRecords_SupplierID ON AccreditationRecords(SupplierID);
CREATE INDEX IX_AccreditationRecords_ReviewDate ON AccreditationRecords(ReviewDate);
CREATE INDEX IX_ActivityLog_SupplierID ON ActivityLog(SupplierID);
CREATE INDEX IX_ActivityLog_PerformedDate ON ActivityLog(PerformedDate);

-- إدراج البيانات الأساسية
-- Insert Initial Data

-- إدراج أنواع الوثائق
INSERT INTO DocumentTypes (TypeName, Description, IsRequired, ValidityPeriodMonths) VALUES
(N'الرخصة التجارية', N'رخصة مزاولة النشاط التجاري', 1, 12),
(N'شهادة الملاءة المالية', N'شهادة من البنك تؤكد الوضع المالي', 1, 6),
(N'شهادة التأمين', N'شهادة تأمين ضد المخاطر', 1, 12),
(N'السجل التجاري', N'السجل التجاري للشركة', 1, 12),
(N'شهادة الجودة ISO', N'شهادة الجودة الدولية', 0, 36),
(N'تصريح البلدية', N'تصريح من البلدية لمزاولة النشاط', 1, 12),
(N'شهادة الضريبة', N'شهادة براءة ذمة ضريبية', 1, 12);

-- إدراج فئات الموردين
INSERT INTO SupplierCategories (CategoryName, Description) VALUES
(N'مواد غذائية', N'موردو المواد الغذائية والمشروبات'),
(N'مواد تنظيف', N'موردو مواد التنظيف والصيانة'),
(N'أجهزة كهربائية', N'موردو الأجهزة الكهربائية والإلكترونية'),
(N'أثاث ومفروشات', N'موردو الأثاث والمفروشات'),
(N'قرطاسية ومكتبية', N'موردو القرطاسية واللوازم المكتبية'),
(N'خدمات صيانة', N'مقدمو خدمات الصيانة والإصلاح'),
(N'خدمات نقل', N'مقدمو خدمات النقل والتوصيل'),
(N'مواد بناء', N'موردو مواد البناء والإنشاءات');

-- إدراج أعضاء لجنة المشتريات
INSERT INTO ProcurementCommittee (MemberName, Position, Email, PhoneNumber, IsChairman) VALUES
(N'أحمد محمد الصالح', N'رئيس لجنة المشتريات', N'<EMAIL>', N'99887766', 1),
(N'فاطمة علي الكندري', N'عضو لجنة المشتريات', N'<EMAIL>', N'99887767', 0),
(N'خالد سعد العتيبي', N'عضو لجنة المشتريات', N'<EMAIL>', N'99887768', 0),
(N'نورا حسن الرشيد', N'أمين سر اللجنة', N'<EMAIL>', N'99887769', 0);

GO

-- إنشاء الإجراءات المخزنة
-- Create Stored Procedures

-- إجراء للحصول على إحصائيات الموردين
CREATE PROCEDURE GetSupplierStatistics
AS
BEGIN
    SELECT 
        Status,
        COUNT(*) as Count,
        CAST(COUNT(*) * 100.0 / (SELECT COUNT(*) FROM Suppliers WHERE IsActive = 1) AS DECIMAL(5,2)) as Percentage
    FROM Suppliers 
    WHERE IsActive = 1
    GROUP BY Status
    ORDER BY Count DESC;
END
GO

-- إجراء للحصول على الوثائق المنتهية الصلاحية
CREATE PROCEDURE GetExpiringDocuments
    @DaysAhead INT = 30
AS
BEGIN
    SELECT 
        s.SupplierName,
        s.LicenseNumber,
        dt.TypeName,
        sd.ExpiryDate,
        DATEDIFF(DAY, GETDATE(), sd.ExpiryDate) as DaysUntilExpiry
    FROM SupplierDocuments sd
    INNER JOIN Suppliers s ON sd.SupplierID = s.SupplierID
    INNER JOIN DocumentTypes dt ON sd.DocumentTypeID = dt.DocumentTypeID
    WHERE sd.IsActive = 1 
        AND s.IsActive = 1
        AND sd.ExpiryDate <= DATEADD(DAY, @DaysAhead, GETDATE())
        AND sd.ExpiryDate >= GETDATE()
    ORDER BY sd.ExpiryDate ASC;
END
GO

-- إجراء لتحديث حالة المورد
CREATE PROCEDURE UpdateSupplierStatus
    @SupplierID INT,
    @NewStatus NVARCHAR(20),
    @ReviewerID INT,
    @Notes NVARCHAR(1000),
    @UpdatedBy NVARCHAR(100)
AS
BEGIN
    DECLARE @OldStatus NVARCHAR(20);
    
    -- الحصول على الحالة الحالية
    SELECT @OldStatus = Status FROM Suppliers WHERE SupplierID = @SupplierID;
    
    -- تحديث حالة المورد
    UPDATE Suppliers 
    SET Status = @NewStatus, 
        ModifiedDate = GETDATE(),
        ModifiedBy = @UpdatedBy
    WHERE SupplierID = @SupplierID;
    
    -- إضافة سجل في جدول الاعتماد
    INSERT INTO AccreditationRecords (SupplierID, ReviewerID, PreviousStatus, NewStatus, ReviewNotes)
    VALUES (@SupplierID, @ReviewerID, @OldStatus, @NewStatus, @Notes);
    
    -- إضافة سجل في جدول النشاطات
    INSERT INTO ActivityLog (SupplierID, ActivityType, ActivityDescription, PerformedBy)
    VALUES (@SupplierID, N'تحديث الحالة', N'تم تغيير حالة المورد من ' + @OldStatus + N' إلى ' + @NewStatus, @UpdatedBy);
END
GO

PRINT N'تم إنشاء قاعدة البيانات بنجاح!';
PRINT N'Database created successfully!';
