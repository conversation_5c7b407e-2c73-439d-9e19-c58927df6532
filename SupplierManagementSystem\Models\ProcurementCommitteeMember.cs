using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;

namespace SupplierManagementSystem.Models
{
    /// <summary>
    /// نموذج بيانات عضو لجنة المشتريات
    /// Procurement Committee Member Data Model
    /// </summary>
    public class ProcurementCommitteeMember
    {
        [Key]
        public int CommitteeID { get; set; }

        [Required(ErrorMessage = "اسم العضو مطلوب")]
        [StringLength(100, ErrorMessage = "اسم العضو يجب أن يكون أقل من 100 حرف")]
        [Display(Name = "اسم العضو")]
        public string MemberName { get; set; } = string.Empty;

        [StringLength(100, ErrorMessage = "المنصب يجب أن يكون أقل من 100 حرف")]
        [Display(Name = "المنصب")]
        public string? Position { get; set; }

        [StringLength(100, ErrorMessage = "البريد الإلكتروني يجب أن يكون أقل من 100 حرف")]
        [Display(Name = "البريد الإلكتروني")]
        [EmailAddress(ErrorMessage = "البريد الإلكتروني غير صحيح")]
        public string? Email { get; set; }

        [StringLength(20, ErrorMessage = "رقم الهاتف يجب أن يكون أقل من 20 حرف")]
        [Display(Name = "رقم الهاتف")]
        [Phone(ErrorMessage = "رقم الهاتف غير صحيح")]
        public string? PhoneNumber { get; set; }

        [Display(Name = "رئيس اللجنة")]
        public bool IsChairman { get; set; } = false;

        [Display(Name = "نشط")]
        public bool IsActive { get; set; } = true;

        [Display(Name = "تاريخ الانضمام")]
        [DataType(DataType.Date)]
        public DateTime JoinDate { get; set; } = DateTime.Today;

        [StringLength(500, ErrorMessage = "الملاحظات يجب أن تكون أقل من 500 حرف")]
        [Display(Name = "ملاحظات")]
        public string? Notes { get; set; }

        // Navigation Properties
        public virtual ICollection<AccreditationRecord> AccreditationRecords { get; set; } = new List<AccreditationRecord>();
        public virtual ICollection<SupplierEvaluation> SupplierEvaluations { get; set; } = new List<SupplierEvaluation>();

        // Methods
        public override string ToString()
        {
            return $"{MemberName} - {Position}";
        }
    }
}
