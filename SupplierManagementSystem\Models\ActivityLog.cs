using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace SupplierManagementSystem.Models
{
    /// <summary>
    /// نموذج بيانات سجل النشاطات
    /// Activity Log Data Model
    /// </summary>
    public class ActivityLog
    {
        [Key]
        public int LogID { get; set; }

        [Display(Name = "معرف المورد")]
        public int? SupplierID { get; set; }

        [Required(ErrorMessage = "نوع النشاط مطلوب")]
        [StringLength(50, ErrorMessage = "نوع النشاط يجب أن يكون أقل من 50 حرف")]
        [Display(Name = "نوع النشاط")]
        public string ActivityType { get; set; } = string.Empty;

        [Required(ErrorMessage = "وصف النشاط مطلوب")]
        [StringLength(500, ErrorMessage = "وصف النشاط يجب أن يكون أقل من 500 حرف")]
        [Display(Name = "وصف النشاط")]
        public string ActivityDescription { get; set; } = string.Empty;

        [Required(ErrorMessage = "اسم المستخدم مطلوب")]
        [StringLength(100, ErrorMessage = "اسم المستخدم يجب أن يكون أقل من 100 حرف")]
        [Display(Name = "نُفذ بواسطة")]
        public string PerformedBy { get; set; } = string.Empty;

        [Display(Name = "تاريخ التنفيذ")]
        public DateTime PerformedDate { get; set; } = DateTime.Now;

        [StringLength(45)]
        [Display(Name = "عنوان IP")]
        public string? IPAddress { get; set; }

        [StringLength(500)]
        [Display(Name = "معلومات المتصفح")]
        public string? UserAgent { get; set; }

        // Navigation Properties
        [ForeignKey("SupplierID")]
        public virtual Supplier? Supplier { get; set; }

        // Computed Properties
        [NotMapped]
        [Display(Name = "أيقونة النشاط")]
        public string ActivityIcon => ActivityType switch
        {
            "إنشاء" => "➕",
            "تحديث" => "✏️",
            "حذف" => "🗑️",
            "تحديث الحالة" => "🔄",
            "رفع وثيقة" => "📄",
            "حذف وثيقة" => "🗑️",
            "تقييم" => "⭐",
            "مراجعة" => "👁️",
            "اعتماد" => "✅",
            "رفض" => "❌",
            _ => "📝"
        };

        [NotMapped]
        [Display(Name = "لون النشاط")]
        public string ActivityColor => ActivityType switch
        {
            "إنشاء" => "#10b981",
            "تحديث" => "#3b82f6",
            "حذف" => "#ef4444",
            "تحديث الحالة" => "#f59e0b",
            "رفع وثيقة" => "#8b5cf6",
            "حذف وثيقة" => "#ef4444",
            "تقييم" => "#f59e0b",
            "مراجعة" => "#6b7280",
            "اعتماد" => "#10b981",
            "رفض" => "#ef4444",
            _ => "#6b7280"
        };

        [NotMapped]
        [Display(Name = "الوقت المنقضي")]
        public string TimeAgo
        {
            get
            {
                var timeSpan = DateTime.Now - PerformedDate;
                
                if (timeSpan.TotalDays >= 1)
                    return $"منذ {(int)timeSpan.TotalDays} يوم";
                else if (timeSpan.TotalHours >= 1)
                    return $"منذ {(int)timeSpan.TotalHours} ساعة";
                else if (timeSpan.TotalMinutes >= 1)
                    return $"منذ {(int)timeSpan.TotalMinutes} دقيقة";
                else
                    return "الآن";
            }
        }

        // Methods
        public override string ToString()
        {
            return $"{ActivityType}: {ActivityDescription} - {PerformedBy}";
        }
    }

    /// <summary>
    /// تعداد أنواع النشاطات
    /// Activity Type Enumeration
    /// </summary>
    public static class ActivityType
    {
        public const string Create = "إنشاء";
        public const string Update = "تحديث";
        public const string Delete = "حذف";
        public const string StatusUpdate = "تحديث الحالة";
        public const string DocumentUpload = "رفع وثيقة";
        public const string DocumentDelete = "حذف وثيقة";
        public const string Evaluation = "تقييم";
        public const string Review = "مراجعة";
        public const string Approve = "اعتماد";
        public const string Reject = "رفض";

        public static readonly string[] AllTypes = 
        { 
            Create, Update, Delete, StatusUpdate, DocumentUpload, 
            DocumentDelete, Evaluation, Review, Approve, Reject 
        };
    }
}
