﻿#pragma checksum "..\..\..\MainWindow.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "0CEF7E214A342ACD0D40A021C475EA47482BDE59"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using SupplierManagementSystem;
using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace SupplierManagementSystem {
    
    
    /// <summary>
    /// MainWindow
    /// </summary>
    public partial class MainWindow : System.Windows.Window, System.Windows.Markup.IComponentConnector, System.Windows.Markup.IStyleConnector {
        
        
        #line 120 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button BtnDashboard;
        
        #line default
        #line hidden
        
        
        #line 122 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button BtnSuppliers;
        
        #line default
        #line hidden
        
        
        #line 124 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button BtnAccreditation;
        
        #line default
        #line hidden
        
        
        #line 126 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button BtnReports;
        
        #line default
        #line hidden
        
        
        #line 128 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button BtnSettings;
        
        #line default
        #line hidden
        
        
        #line 136 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Grid MainContent;
        
        #line default
        #line hidden
        
        
        #line 138 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.StackPanel DashboardPanel;
        
        #line default
        #line hidden
        
        
        #line 155 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock TotalSuppliersText;
        
        #line default
        #line hidden
        
        
        #line 166 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock ApprovedSuppliersText;
        
        #line default
        #line hidden
        
        
        #line 177 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock PendingSuppliersText;
        
        #line default
        #line hidden
        
        
        #line 188 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock ExpiringDocumentsText;
        
        #line default
        #line hidden
        
        
        #line 212 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button BtnAddSupplier;
        
        #line default
        #line hidden
        
        
        #line 224 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button BtnSearchSuppliers;
        
        #line default
        #line hidden
        
        
        #line 236 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button BtnReviewRequests;
        
        #line default
        #line hidden
        
        
        #line 248 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ListView RecentActivitiesListView;
        
        #line default
        #line hidden
        
        
        #line 264 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.StackPanel SuppliersPanel;
        
        #line default
        #line hidden
        
        
        #line 277 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox TxtSearchSuppliers;
        
        #line default
        #line hidden
        
        
        #line 285 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox CmbStatusFilter;
        
        #line default
        #line hidden
        
        
        #line 297 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox CmbCategoryFilter;
        
        #line default
        #line hidden
        
        
        #line 302 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button BtnAddNewSupplier;
        
        #line default
        #line hidden
        
        
        #line 314 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.DataGrid SuppliersDataGrid;
        
        #line default
        #line hidden
        
        
        #line 371 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock TxtSuppliersCount;
        
        #line default
        #line hidden
        
        
        #line 379 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.StackPanel AccreditationPanel;
        
        #line default
        #line hidden
        
        
        #line 393 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock TxtPendingCount;
        
        #line default
        #line hidden
        
        
        #line 403 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock TxtTodayReviews;
        
        #line default
        #line hidden
        
        
        #line 413 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock TxtUrgentReviews;
        
        #line default
        #line hidden
        
        
        #line 426 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button BtnRefreshAccreditation;
        
        #line default
        #line hidden
        
        
        #line 432 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.DataGrid AccreditationDataGrid;
        
        #line default
        #line hidden
        
        
        #line 493 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.StackPanel ReportsPanel;
        
        #line default
        #line hidden
        
        
        #line 507 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button BtnSuppliersStatusReport;
        
        #line default
        #line hidden
        
        
        #line 512 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button BtnExpiringDocumentsReport;
        
        #line default
        #line hidden
        
        
        #line 517 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button BtnActivitiesReport;
        
        #line default
        #line hidden
        
        
        #line 522 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button BtnCategoriesReport;
        
        #line default
        #line hidden
        
        
        #line 535 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.StackPanel StatusStatisticsPanel;
        
        #line default
        #line hidden
        
        
        #line 541 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.StackPanel CategoryStatisticsPanel;
        
        #line default
        #line hidden
        
        
        #line 552 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock TxtReportTitle;
        
        #line default
        #line hidden
        
        
        #line 554 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button BtnPrintReport;
        
        #line default
        #line hidden
        
        
        #line 557 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button BtnExportReport;
        
        #line default
        #line hidden
        
        
        #line 564 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ScrollViewer ReportContentScrollViewer;
        
        #line default
        #line hidden
        
        
        #line 565 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.StackPanel ReportContentPanel;
        
        #line default
        #line hidden
        
        
        #line 579 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.StackPanel SettingsPanel;
        
        #line default
        #line hidden
        
        
        #line 594 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox TxtApplicationName;
        
        #line default
        #line hidden
        
        
        #line 599 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox TxtCompanyName;
        
        #line default
        #line hidden
        
        
        #line 604 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox TxtDocumentWarningDays;
        
        #line default
        #line hidden
        
        
        #line 608 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox ChkAutoBackup;
        
        #line default
        #line hidden
        
        
        #line 613 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox ChkShowNotifications;
        
        #line default
        #line hidden
        
        
        #line 631 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox TxtDatabasePath;
        
        #line default
        #line hidden
        
        
        #line 641 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock TxtDatabaseSize;
        
        #line default
        #line hidden
        
        
        #line 644 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button BtnBackupDatabase;
        
        #line default
        #line hidden
        
        
        #line 650 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button BtnRestoreDatabase;
        
        #line default
        #line hidden
        
        
        #line 656 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button BtnOptimizeDatabase;
        
        #line default
        #line hidden
        
        
        #line 668 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button BtnSaveSettings;
        
        #line default
        #line hidden
        
        
        #line 671 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button BtnResetSettings;
        
        #line default
        #line hidden
        
        
        #line 694 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.StackPanel LoadingIndicator;
        
        #line default
        #line hidden
        
        
        #line 697 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock TxtLoadingMessage;
        
        #line default
        #line hidden
        
        
        #line 702 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock StatusText;
        
        #line default
        #line hidden
        
        
        #line 703 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock CurrentTimeText;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.7.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/SupplierManagementSystem;V1.0.0.0;component/mainwindow.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\MainWindow.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.7.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            this.BtnDashboard = ((System.Windows.Controls.Button)(target));
            
            #line 121 "..\..\..\MainWindow.xaml"
            this.BtnDashboard.Click += new System.Windows.RoutedEventHandler(this.BtnDashboard_Click);
            
            #line default
            #line hidden
            return;
            case 2:
            this.BtnSuppliers = ((System.Windows.Controls.Button)(target));
            
            #line 123 "..\..\..\MainWindow.xaml"
            this.BtnSuppliers.Click += new System.Windows.RoutedEventHandler(this.BtnSuppliers_Click);
            
            #line default
            #line hidden
            return;
            case 3:
            this.BtnAccreditation = ((System.Windows.Controls.Button)(target));
            
            #line 125 "..\..\..\MainWindow.xaml"
            this.BtnAccreditation.Click += new System.Windows.RoutedEventHandler(this.BtnAccreditation_Click);
            
            #line default
            #line hidden
            return;
            case 4:
            this.BtnReports = ((System.Windows.Controls.Button)(target));
            
            #line 127 "..\..\..\MainWindow.xaml"
            this.BtnReports.Click += new System.Windows.RoutedEventHandler(this.BtnReports_Click);
            
            #line default
            #line hidden
            return;
            case 5:
            this.BtnSettings = ((System.Windows.Controls.Button)(target));
            
            #line 129 "..\..\..\MainWindow.xaml"
            this.BtnSettings.Click += new System.Windows.RoutedEventHandler(this.BtnSettings_Click);
            
            #line default
            #line hidden
            return;
            case 6:
            this.MainContent = ((System.Windows.Controls.Grid)(target));
            return;
            case 7:
            this.DashboardPanel = ((System.Windows.Controls.StackPanel)(target));
            return;
            case 8:
            this.TotalSuppliersText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 9:
            this.ApprovedSuppliersText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 10:
            this.PendingSuppliersText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 11:
            this.ExpiringDocumentsText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 12:
            this.BtnAddSupplier = ((System.Windows.Controls.Button)(target));
            
            #line 214 "..\..\..\MainWindow.xaml"
            this.BtnAddSupplier.Click += new System.Windows.RoutedEventHandler(this.BtnAddSupplier_Click);
            
            #line default
            #line hidden
            return;
            case 13:
            this.BtnSearchSuppliers = ((System.Windows.Controls.Button)(target));
            
            #line 226 "..\..\..\MainWindow.xaml"
            this.BtnSearchSuppliers.Click += new System.Windows.RoutedEventHandler(this.BtnSearchSuppliers_Click);
            
            #line default
            #line hidden
            return;
            case 14:
            this.BtnReviewRequests = ((System.Windows.Controls.Button)(target));
            
            #line 238 "..\..\..\MainWindow.xaml"
            this.BtnReviewRequests.Click += new System.Windows.RoutedEventHandler(this.BtnReviewRequests_Click);
            
            #line default
            #line hidden
            return;
            case 15:
            this.RecentActivitiesListView = ((System.Windows.Controls.ListView)(target));
            return;
            case 16:
            this.SuppliersPanel = ((System.Windows.Controls.StackPanel)(target));
            return;
            case 17:
            this.TxtSearchSuppliers = ((System.Windows.Controls.TextBox)(target));
            
            #line 281 "..\..\..\MainWindow.xaml"
            this.TxtSearchSuppliers.GotFocus += new System.Windows.RoutedEventHandler(this.TxtSearchSuppliers_GotFocus);
            
            #line default
            #line hidden
            
            #line 282 "..\..\..\MainWindow.xaml"
            this.TxtSearchSuppliers.LostFocus += new System.Windows.RoutedEventHandler(this.TxtSearchSuppliers_LostFocus);
            
            #line default
            #line hidden
            
            #line 283 "..\..\..\MainWindow.xaml"
            this.TxtSearchSuppliers.TextChanged += new System.Windows.Controls.TextChangedEventHandler(this.TxtSearchSuppliers_TextChanged);
            
            #line default
            #line hidden
            return;
            case 18:
            this.CmbStatusFilter = ((System.Windows.Controls.ComboBox)(target));
            
            #line 288 "..\..\..\MainWindow.xaml"
            this.CmbStatusFilter.SelectionChanged += new System.Windows.Controls.SelectionChangedEventHandler(this.CmbStatusFilter_SelectionChanged);
            
            #line default
            #line hidden
            return;
            case 19:
            this.CmbCategoryFilter = ((System.Windows.Controls.ComboBox)(target));
            
            #line 300 "..\..\..\MainWindow.xaml"
            this.CmbCategoryFilter.SelectionChanged += new System.Windows.Controls.SelectionChangedEventHandler(this.CmbCategoryFilter_SelectionChanged);
            
            #line default
            #line hidden
            return;
            case 20:
            this.BtnAddNewSupplier = ((System.Windows.Controls.Button)(target));
            
            #line 305 "..\..\..\MainWindow.xaml"
            this.BtnAddNewSupplier.Click += new System.Windows.RoutedEventHandler(this.BtnAddNewSupplier_Click);
            
            #line default
            #line hidden
            return;
            case 21:
            this.SuppliersDataGrid = ((System.Windows.Controls.DataGrid)(target));
            
            #line 323 "..\..\..\MainWindow.xaml"
            this.SuppliersDataGrid.MouseDoubleClick += new System.Windows.Input.MouseButtonEventHandler(this.SuppliersDataGrid_MouseDoubleClick);
            
            #line default
            #line hidden
            return;
            case 25:
            this.TxtSuppliersCount = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 26:
            this.AccreditationPanel = ((System.Windows.Controls.StackPanel)(target));
            return;
            case 27:
            this.TxtPendingCount = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 28:
            this.TxtTodayReviews = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 29:
            this.TxtUrgentReviews = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 30:
            this.BtnRefreshAccreditation = ((System.Windows.Controls.Button)(target));
            
            #line 429 "..\..\..\MainWindow.xaml"
            this.BtnRefreshAccreditation.Click += new System.Windows.RoutedEventHandler(this.BtnRefreshAccreditation_Click);
            
            #line default
            #line hidden
            return;
            case 31:
            this.AccreditationDataGrid = ((System.Windows.Controls.DataGrid)(target));
            return;
            case 35:
            this.ReportsPanel = ((System.Windows.Controls.StackPanel)(target));
            return;
            case 36:
            this.BtnSuppliersStatusReport = ((System.Windows.Controls.Button)(target));
            
            #line 510 "..\..\..\MainWindow.xaml"
            this.BtnSuppliersStatusReport.Click += new System.Windows.RoutedEventHandler(this.BtnSuppliersStatusReport_Click);
            
            #line default
            #line hidden
            return;
            case 37:
            this.BtnExpiringDocumentsReport = ((System.Windows.Controls.Button)(target));
            
            #line 515 "..\..\..\MainWindow.xaml"
            this.BtnExpiringDocumentsReport.Click += new System.Windows.RoutedEventHandler(this.BtnExpiringDocumentsReport_Click);
            
            #line default
            #line hidden
            return;
            case 38:
            this.BtnActivitiesReport = ((System.Windows.Controls.Button)(target));
            
            #line 520 "..\..\..\MainWindow.xaml"
            this.BtnActivitiesReport.Click += new System.Windows.RoutedEventHandler(this.BtnActivitiesReport_Click);
            
            #line default
            #line hidden
            return;
            case 39:
            this.BtnCategoriesReport = ((System.Windows.Controls.Button)(target));
            
            #line 525 "..\..\..\MainWindow.xaml"
            this.BtnCategoriesReport.Click += new System.Windows.RoutedEventHandler(this.BtnCategoriesReport_Click);
            
            #line default
            #line hidden
            return;
            case 40:
            this.StatusStatisticsPanel = ((System.Windows.Controls.StackPanel)(target));
            return;
            case 41:
            this.CategoryStatisticsPanel = ((System.Windows.Controls.StackPanel)(target));
            return;
            case 42:
            this.TxtReportTitle = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 43:
            this.BtnPrintReport = ((System.Windows.Controls.Button)(target));
            
            #line 556 "..\..\..\MainWindow.xaml"
            this.BtnPrintReport.Click += new System.Windows.RoutedEventHandler(this.BtnPrintReport_Click);
            
            #line default
            #line hidden
            return;
            case 44:
            this.BtnExportReport = ((System.Windows.Controls.Button)(target));
            
            #line 559 "..\..\..\MainWindow.xaml"
            this.BtnExportReport.Click += new System.Windows.RoutedEventHandler(this.BtnExportReport_Click);
            
            #line default
            #line hidden
            return;
            case 45:
            this.ReportContentScrollViewer = ((System.Windows.Controls.ScrollViewer)(target));
            return;
            case 46:
            this.ReportContentPanel = ((System.Windows.Controls.StackPanel)(target));
            return;
            case 47:
            this.SettingsPanel = ((System.Windows.Controls.StackPanel)(target));
            return;
            case 48:
            this.TxtApplicationName = ((System.Windows.Controls.TextBox)(target));
            return;
            case 49:
            this.TxtCompanyName = ((System.Windows.Controls.TextBox)(target));
            return;
            case 50:
            this.TxtDocumentWarningDays = ((System.Windows.Controls.TextBox)(target));
            return;
            case 51:
            this.ChkAutoBackup = ((System.Windows.Controls.CheckBox)(target));
            return;
            case 52:
            this.ChkShowNotifications = ((System.Windows.Controls.CheckBox)(target));
            return;
            case 53:
            this.TxtDatabasePath = ((System.Windows.Controls.TextBox)(target));
            return;
            case 54:
            
            #line 637 "..\..\..\MainWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.BtnBrowseDatabasePath_Click);
            
            #line default
            #line hidden
            return;
            case 55:
            this.TxtDatabaseSize = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 56:
            this.BtnBackupDatabase = ((System.Windows.Controls.Button)(target));
            
            #line 648 "..\..\..\MainWindow.xaml"
            this.BtnBackupDatabase.Click += new System.Windows.RoutedEventHandler(this.BtnBackupDatabase_Click);
            
            #line default
            #line hidden
            return;
            case 57:
            this.BtnRestoreDatabase = ((System.Windows.Controls.Button)(target));
            
            #line 654 "..\..\..\MainWindow.xaml"
            this.BtnRestoreDatabase.Click += new System.Windows.RoutedEventHandler(this.BtnRestoreDatabase_Click);
            
            #line default
            #line hidden
            return;
            case 58:
            this.BtnOptimizeDatabase = ((System.Windows.Controls.Button)(target));
            
            #line 660 "..\..\..\MainWindow.xaml"
            this.BtnOptimizeDatabase.Click += new System.Windows.RoutedEventHandler(this.BtnOptimizeDatabase_Click);
            
            #line default
            #line hidden
            return;
            case 59:
            this.BtnSaveSettings = ((System.Windows.Controls.Button)(target));
            
            #line 670 "..\..\..\MainWindow.xaml"
            this.BtnSaveSettings.Click += new System.Windows.RoutedEventHandler(this.BtnSaveSettings_Click);
            
            #line default
            #line hidden
            return;
            case 60:
            this.BtnResetSettings = ((System.Windows.Controls.Button)(target));
            
            #line 673 "..\..\..\MainWindow.xaml"
            this.BtnResetSettings.Click += new System.Windows.RoutedEventHandler(this.BtnResetSettings_Click);
            
            #line default
            #line hidden
            return;
            case 61:
            this.LoadingIndicator = ((System.Windows.Controls.StackPanel)(target));
            return;
            case 62:
            this.TxtLoadingMessage = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 63:
            this.StatusText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 64:
            this.CurrentTimeText = ((System.Windows.Controls.TextBlock)(target));
            return;
            }
            this._contentLoaded = true;
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.7.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        void System.Windows.Markup.IStyleConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 22:
            
            #line 351 "..\..\..\MainWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.BtnViewSupplier_Click);
            
            #line default
            #line hidden
            break;
            case 23:
            
            #line 355 "..\..\..\MainWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.BtnEditSupplier_Click);
            
            #line default
            #line hidden
            break;
            case 24:
            
            #line 361 "..\..\..\MainWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.BtnDeleteSupplier_Click);
            
            #line default
            #line hidden
            break;
            case 32:
            
            #line 469 "..\..\..\MainWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.BtnApproveSupplier_Click);
            
            #line default
            #line hidden
            break;
            case 33:
            
            #line 475 "..\..\..\MainWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.BtnRejectSupplier_Click);
            
            #line default
            #line hidden
            break;
            case 34:
            
            #line 481 "..\..\..\MainWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.BtnPostponeSupplier_Click);
            
            #line default
            #line hidden
            break;
            }
        }
    }
}

