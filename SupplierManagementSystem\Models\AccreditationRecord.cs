using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace SupplierManagementSystem.Models
{
    /// <summary>
    /// نموذج بيانات سجل الاعتماد
    /// Accreditation Record Data Model
    /// </summary>
    public class AccreditationRecord
    {
        [Key]
        public int RecordID { get; set; }

        [Required]
        [Display(Name = "معرف المورد")]
        public int SupplierID { get; set; }

        [Display(Name = "تاريخ المراجعة")]
        public DateTime ReviewDate { get; set; } = DateTime.Now;

        [Display(Name = "معرف المراجع")]
        public int? ReviewerID { get; set; }

        [StringLength(20)]
        [Display(Name = "الحالة السابقة")]
        public string? PreviousStatus { get; set; }

        [Required]
        [StringLength(20)]
        [Display(Name = "الحالة الجديدة")]
        public string NewStatus { get; set; } = string.Empty;

        [Required]
        [StringLength(20)]
        [Display(Name = "القرار")]
        public string Decision { get; set; } = string.Empty;

        [StringLength(1000)]
        [Display(Name = "ملاحظات المراجعة")]
        public string? ReviewNotes { get; set; }

        [StringLength(1000)]
        [Display(Name = "قرار اللجنة")]
        public string? CommitteeDecision { get; set; }

        [Display(Name = "تاريخ المراجعة القادمة")]
        [DataType(DataType.Date)]
        public DateTime? NextReviewDate { get; set; }

        [Display(Name = "نشط")]
        public bool IsActive { get; set; } = true;

        // Navigation Properties
        [ForeignKey("SupplierID")]
        public virtual Supplier? Supplier { get; set; }

        [ForeignKey("ReviewerID")]
        public virtual ProcurementCommitteeMember? Reviewer { get; set; }

        // Computed Properties
        [NotMapped]
        [Display(Name = "لون القرار")]
        public string DecisionColor => Decision switch
        {
            "موافقة" => "#10b981",
            "رفض" => "#ef4444",
            "تأجيل" => "#f59e0b",
            "طلب مستندات إضافية" => "#3b82f6",
            _ => "#6b7280"
        };

        [NotMapped]
        [Display(Name = "أيقونة القرار")]
        public string DecisionIcon => Decision switch
        {
            "موافقة" => "✅",
            "رفض" => "❌",
            "تأجيل" => "⏸️",
            "طلب مستندات إضافية" => "📄",
            _ => "❓"
        };

        [NotMapped]
        [Display(Name = "الأيام منذ المراجعة")]
        public int DaysSinceReview => (int)(DateTime.Today - ReviewDate.Date).TotalDays;

        [NotMapped]
        [Display(Name = "الأيام حتى المراجعة القادمة")]
        public int? DaysUntilNextReview => NextReviewDate.HasValue ? 
            (int)(NextReviewDate.Value.Date - DateTime.Today).TotalDays : null;

        // Methods
        public bool IsRecentReview(int daysThreshold = 7)
        {
            return DaysSinceReview <= daysThreshold;
        }

        public bool IsNextReviewDue()
        {
            return NextReviewDate.HasValue && NextReviewDate.Value.Date <= DateTime.Today;
        }

        public override string ToString()
        {
            return $"{Decision} - {ReviewDate:yyyy/MM/dd}";
        }
    }

    /// <summary>
    /// تعداد قرارات الاعتماد
    /// Accreditation Decision Enumeration
    /// </summary>
    public static class AccreditationDecision
    {
        public const string Approve = "موافقة";
        public const string Reject = "رفض";
        public const string Postpone = "تأجيل";
        public const string RequestDocuments = "طلب مستندات إضافية";

        public static readonly string[] AllDecisions = { Approve, Reject, Postpone, RequestDocuments };

        public static string GetDisplayName(string decision)
        {
            return decision switch
            {
                Approve => "موافقة",
                Reject => "رفض",
                Postpone => "تأجيل",
                RequestDocuments => "طلب مستندات إضافية",
                _ => "غير محدد"
            };
        }
    }
}
