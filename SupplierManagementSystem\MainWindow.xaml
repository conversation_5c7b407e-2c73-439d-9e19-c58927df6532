﻿<Window x:Class="SupplierManagementSystem.MainWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:local="clr-namespace:SupplierManagementSystem"
        mc:Ignorable="d"
        Title="نظام إدارة الموردين - جمعية المنقف التعاونية"
        Height="900" Width="1400"
        WindowState="Maximized"
        WindowStartupLocation="CenterScreen"
        FlowDirection="RightToLeft"
        FontFamily="Segoe UI, Tahoma"
        FontSize="14">

    <Window.Resources>
        <!-- تعريف الألوان والأنماط -->
        <SolidColorBrush x:Key="PrimaryColor" Color="#0f766e"/>
        <SolidColorBrush x:Key="PrimaryLightColor" Color="#14b8a6"/>
        <SolidColorBrush x:Key="SecondaryColor" Color="#f59e0b"/>
        <SolidColorBrush x:Key="AccentColor" Color="#3b82f6"/>
        <SolidColorBrush x:Key="SuccessColor" Color="#10b981"/>
        <SolidColorBrush x:Key="WarningColor" Color="#f59e0b"/>
        <SolidColorBrush x:Key="ErrorColor" Color="#ef4444"/>
        <SolidColorBrush x:Key="BackgroundColor" Color="#f8fafc"/>
        <SolidColorBrush x:Key="SurfaceColor" Color="#ffffff"/>
        <SolidColorBrush x:Key="TextPrimaryColor" Color="#1e293b"/>
        <SolidColorBrush x:Key="TextSecondaryColor" Color="#64748b"/>

        <!-- أنماط الأزرار -->
        <Style x:Key="PrimaryButtonStyle" TargetType="Button">
            <Setter Property="Background" Value="{StaticResource PrimaryColor}"/>
            <Setter Property="Foreground" Value="White"/>
            <Setter Property="BorderThickness" Value="0"/>
            <Setter Property="Padding" Value="16,8"/>
            <Setter Property="Margin" Value="4"/>
            <Setter Property="FontWeight" Value="SemiBold"/>
            <Setter Property="Cursor" Value="Hand"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Border Background="{TemplateBinding Background}"
                                CornerRadius="8"
                                Padding="{TemplateBinding Padding}">
                            <ContentPresenter HorizontalAlignment="Center"
                                            VerticalAlignment="Center"/>
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter Property="Background" Value="{StaticResource PrimaryLightColor}"/>
                            </Trigger>
                            <Trigger Property="IsPressed" Value="True">
                                <Setter Property="Background" Value="{StaticResource PrimaryColor}"/>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>

        <!-- أنماط البطاقات -->
        <Style x:Key="CardStyle" TargetType="Border">
            <Setter Property="Background" Value="{StaticResource SurfaceColor}"/>
            <Setter Property="CornerRadius" Value="12"/>
            <Setter Property="Padding" Value="20"/>
            <Setter Property="Margin" Value="8"/>
            <Setter Property="Effect">
                <Setter.Value>
                    <DropShadowEffect Color="Gray" Direction="270" ShadowDepth="2" Opacity="0.1" BlurRadius="8"/>
                </Setter.Value>
            </Setter>
        </Style>

        <!-- أنماط النصوص -->
        <Style x:Key="HeaderTextStyle" TargetType="TextBlock">
            <Setter Property="FontSize" Value="24"/>
            <Setter Property="FontWeight" Value="Bold"/>
            <Setter Property="Foreground" Value="{StaticResource TextPrimaryColor}"/>
            <Setter Property="Margin" Value="0,0,0,16"/>
        </Style>

        <Style x:Key="SubHeaderTextStyle" TargetType="TextBlock">
            <Setter Property="FontSize" Value="18"/>
            <Setter Property="FontWeight" Value="SemiBold"/>
            <Setter Property="Foreground" Value="{StaticResource PrimaryColor}"/>
            <Setter Property="Margin" Value="0,0,0,8"/>
        </Style>
    </Window.Resources>

    <Grid Background="{StaticResource BackgroundColor}">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- شريط العنوان والقائمة العلوية -->
        <Border Grid.Row="0" Background="{StaticResource PrimaryColor}" Padding="20,16">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <!-- العنوان والشعار -->
                <StackPanel Grid.Column="0" Orientation="Horizontal">
                    <TextBlock Text="🏢" FontSize="32" VerticalAlignment="Center" Margin="0,0,16,0"/>
                    <StackPanel>
                        <TextBlock Text="نظام إدارة الموردين"
                                 FontSize="24" FontWeight="Bold"
                                 Foreground="White"/>
                        <TextBlock Text="جمعية المنقف التعاونية"
                                 FontSize="14"
                                 Foreground="White" Opacity="0.9"/>
                    </StackPanel>
                </StackPanel>

                <!-- أزرار القائمة العلوية -->
                <StackPanel Grid.Column="1" Orientation="Horizontal">
                    <Button x:Name="BtnDashboard" Content="🏠 الرئيسية" Style="{StaticResource PrimaryButtonStyle}"
                            Background="Transparent" Click="BtnDashboard_Click"/>
                    <Button x:Name="BtnSuppliers" Content="👥 الموردين" Style="{StaticResource PrimaryButtonStyle}"
                            Background="Transparent" Click="BtnSuppliers_Click"/>
                    <Button x:Name="BtnAccreditation" Content="✅ الاعتماد" Style="{StaticResource PrimaryButtonStyle}"
                            Background="Transparent" Click="BtnAccreditation_Click"/>
                    <Button x:Name="BtnReports" Content="📊 التقارير" Style="{StaticResource PrimaryButtonStyle}"
                            Background="Transparent" Click="BtnReports_Click"/>
                    <Button x:Name="BtnSettings" Content="⚙️ الإعدادات" Style="{StaticResource PrimaryButtonStyle}"
                            Background="Transparent" Click="BtnSettings_Click"/>
                </StackPanel>
            </Grid>
        </Border>

        <!-- المحتوى الرئيسي -->
        <ScrollViewer Grid.Row="1" VerticalScrollBarVisibility="Auto" Padding="20">
            <Grid x:Name="MainContent">
                <!-- لوحة المعلومات الرئيسية -->
                <StackPanel x:Name="DashboardPanel" Visibility="Visible">

                    <!-- بطاقات الإحصائيات -->
                    <TextBlock Text="📊 نظرة عامة على النظام" Style="{StaticResource HeaderTextStyle}"/>

                    <Grid Margin="0,0,0,32">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="*"/>
                        </Grid.ColumnDefinitions>

                        <!-- إجمالي الموردين -->
                        <Border Grid.Column="0" Style="{StaticResource CardStyle}">
                            <StackPanel>
                                <TextBlock Text="👥" FontSize="32" HorizontalAlignment="Center" Margin="0,0,0,8"/>
                                <TextBlock x:Name="TotalSuppliersText" Text="150" FontSize="28" FontWeight="Bold"
                                         Foreground="{StaticResource PrimaryColor}" HorizontalAlignment="Center"/>
                                <TextBlock Text="إجمالي الموردين" FontSize="14"
                                         Foreground="{StaticResource TextSecondaryColor}" HorizontalAlignment="Center"/>
                            </StackPanel>
                        </Border>

                        <!-- الموردين المعتمدين -->
                        <Border Grid.Column="1" Style="{StaticResource CardStyle}">
                            <StackPanel>
                                <TextBlock Text="✅" FontSize="32" HorizontalAlignment="Center" Margin="0,0,0,8"/>
                                <TextBlock x:Name="ApprovedSuppliersText" Text="112" FontSize="28" FontWeight="Bold"
                                         Foreground="{StaticResource SuccessColor}" HorizontalAlignment="Center"/>
                                <TextBlock Text="موردين معتمدين" FontSize="14"
                                         Foreground="{StaticResource TextSecondaryColor}" HorizontalAlignment="Center"/>
                            </StackPanel>
                        </Border>

                        <!-- قيد المراجعة -->
                        <Border Grid.Column="2" Style="{StaticResource CardStyle}">
                            <StackPanel>
                                <TextBlock Text="⏳" FontSize="32" HorizontalAlignment="Center" Margin="0,0,0,8"/>
                                <TextBlock x:Name="PendingSuppliersText" Text="23" FontSize="28" FontWeight="Bold"
                                         Foreground="{StaticResource WarningColor}" HorizontalAlignment="Center"/>
                                <TextBlock Text="قيد المراجعة" FontSize="14"
                                         Foreground="{StaticResource TextSecondaryColor}" HorizontalAlignment="Center"/>
                            </StackPanel>
                        </Border>

                        <!-- وثائق تنتهي قريباً -->
                        <Border Grid.Column="3" Style="{StaticResource CardStyle}">
                            <StackPanel>
                                <TextBlock Text="⚠️" FontSize="32" HorizontalAlignment="Center" Margin="0,0,0,8"/>
                                <TextBlock x:Name="ExpiringDocumentsText" Text="12" FontSize="28" FontWeight="Bold"
                                         Foreground="{StaticResource ErrorColor}" HorizontalAlignment="Center"/>
                                <TextBlock Text="وثائق تنتهي قريباً" FontSize="14"
                                         Foreground="{StaticResource TextSecondaryColor}" HorizontalAlignment="Center"/>
                            </StackPanel>
                        </Border>
                    </Grid>

                    <!-- الإجراءات السريعة -->
                    <TextBlock Text="⚡ الإجراءات السريعة" Style="{StaticResource HeaderTextStyle}"/>

                    <Grid Margin="0,0,0,32">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="*"/>
                        </Grid.ColumnDefinitions>

                        <Border Grid.Column="0" Style="{StaticResource CardStyle}">
                            <StackPanel>
                                <TextBlock Text="➕ إضافة مورد جديد" Style="{StaticResource SubHeaderTextStyle}"/>
                                <TextBlock Text="إضافة مورد جديد إلى النظام مع كافة البيانات والوثائق المطلوبة"
                                         TextWrapping="Wrap" Margin="0,0,0,16"
                                         Foreground="{StaticResource TextSecondaryColor}"/>
                                <Button x:Name="BtnAddSupplier" Content="إضافة مورد"
                                      Style="{StaticResource PrimaryButtonStyle}"
                                      HorizontalAlignment="Stretch" Click="BtnAddSupplier_Click"/>
                            </StackPanel>
                        </Border>

                        <Border Grid.Column="1" Style="{StaticResource CardStyle}">
                            <StackPanel>
                                <TextBlock Text="🔍 البحث في الموردين" Style="{StaticResource SubHeaderTextStyle}"/>
                                <TextBlock Text="البحث والفلترة في قائمة الموردين حسب الاسم أو الحالة أو نوع النشاط"
                                         TextWrapping="Wrap" Margin="0,0,0,16"
                                         Foreground="{StaticResource TextSecondaryColor}"/>
                                <Button x:Name="BtnSearchSuppliers" Content="البحث في الموردين"
                                      Style="{StaticResource PrimaryButtonStyle}"
                                      HorizontalAlignment="Stretch" Click="BtnSearchSuppliers_Click"/>
                            </StackPanel>
                        </Border>

                        <Border Grid.Column="2" Style="{StaticResource CardStyle}">
                            <StackPanel>
                                <TextBlock Text="📋 مراجعة الطلبات" Style="{StaticResource SubHeaderTextStyle}"/>
                                <TextBlock Text="مراجعة واعتماد طلبات الموردين الجدد والتحديثات المطلوبة"
                                         TextWrapping="Wrap" Margin="0,0,0,16"
                                         Foreground="{StaticResource TextSecondaryColor}"/>
                                <Button x:Name="BtnReviewRequests" Content="مراجعة الطلبات"
                                      Style="{StaticResource PrimaryButtonStyle}"
                                      HorizontalAlignment="Stretch" Click="BtnReviewRequests_Click"/>
                            </StackPanel>
                        </Border>
                    </Grid>

                    <!-- آخر النشاطات -->
                    <TextBlock Text="📝 آخر النشاطات" Style="{StaticResource HeaderTextStyle}"/>

                    <Border Style="{StaticResource CardStyle}">
                        <StackPanel>
                            <ListView x:Name="RecentActivitiesListView" Height="200" BorderThickness="0">
                                <ListView.View>
                                    <GridView>
                                        <GridViewColumn Header="النشاط" Width="200" DisplayMemberBinding="{Binding ActivityType}"/>
                                        <GridViewColumn Header="الوصف" Width="300" DisplayMemberBinding="{Binding ActivityDescription}"/>
                                        <GridViewColumn Header="المستخدم" Width="150" DisplayMemberBinding="{Binding PerformedBy}"/>
                                        <GridViewColumn Header="التاريخ" Width="150" DisplayMemberBinding="{Binding PerformedDate, StringFormat='{}{0:yyyy/MM/dd HH:mm}'}"/>
                                    </GridView>
                                </ListView.View>
                            </ListView>
                        </StackPanel>
                    </Border>

                </StackPanel>

                <!-- باقي الصفحات ستكون مخفية في البداية -->
                <StackPanel x:Name="SuppliersPanel" Visibility="Collapsed">
                    <TextBlock Text="👥 إدارة الموردين" Style="{StaticResource HeaderTextStyle}"/>
                    <!-- محتوى صفحة الموردين سيتم إضافته لاحقاً -->
                </StackPanel>

                <StackPanel x:Name="AccreditationPanel" Visibility="Collapsed">
                    <TextBlock Text="✅ اعتماد الموردين" Style="{StaticResource HeaderTextStyle}"/>
                    <!-- محتوى صفحة الاعتماد سيتم إضافته لاحقاً -->
                </StackPanel>

                <StackPanel x:Name="ReportsPanel" Visibility="Collapsed">
                    <TextBlock Text="📊 التقارير والإحصائيات" Style="{StaticResource HeaderTextStyle}"/>
                    <!-- محتوى صفحة التقارير سيتم إضافته لاحقاً -->
                </StackPanel>

                <StackPanel x:Name="SettingsPanel" Visibility="Collapsed">
                    <TextBlock Text="⚙️ إعدادات النظام" Style="{StaticResource HeaderTextStyle}"/>
                    <!-- محتوى صفحة الإعدادات سيتم إضافته لاحقاً -->
                </StackPanel>

            </Grid>
        </ScrollViewer>

        <!-- شريط الحالة السفلي -->
        <Border Grid.Row="2" Background="{StaticResource PrimaryColor}" Padding="20,8">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <TextBlock Grid.Column="0" Text="© 2024 جمعية المنقف التعاونية - نظام إدارة الموردين"
                         Foreground="White" VerticalAlignment="Center"/>

                <StackPanel Grid.Column="1" Orientation="Horizontal">
                    <TextBlock x:Name="StatusText" Text="متصل" Foreground="White" VerticalAlignment="Center" Margin="0,0,16,0"/>
                    <TextBlock x:Name="CurrentTimeText" Text="" Foreground="White" VerticalAlignment="Center"/>
                </StackPanel>
            </Grid>
        </Border>

    </Grid>
</Window>