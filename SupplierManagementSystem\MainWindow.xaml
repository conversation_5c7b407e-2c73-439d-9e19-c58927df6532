﻿<Window x:Class="SupplierManagementSystem.MainWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:local="clr-namespace:SupplierManagementSystem"
        mc:Ignorable="d"
        Title="نظام إدارة الموردين - جمعية المنقف التعاونية"
        Height="900" Width="1400"
        WindowState="Maximized"
        WindowStartupLocation="CenterScreen"
        FlowDirection="RightToLeft"
        FontFamily="Segoe UI, Tahoma"
        FontSize="14">

    <Window.Resources>
        <!-- تعريف الألوان والأنماط -->
        <SolidColorBrush x:Key="PrimaryColor" Color="#0f766e"/>
        <SolidColorBrush x:Key="PrimaryLightColor" Color="#14b8a6"/>
        <SolidColorBrush x:Key="SecondaryColor" Color="#f59e0b"/>
        <SolidColorBrush x:Key="AccentColor" Color="#3b82f6"/>
        <SolidColorBrush x:Key="SuccessColor" Color="#10b981"/>
        <SolidColorBrush x:Key="WarningColor" Color="#f59e0b"/>
        <SolidColorBrush x:Key="ErrorColor" Color="#ef4444"/>
        <SolidColorBrush x:Key="BackgroundColor" Color="#f8fafc"/>
        <SolidColorBrush x:Key="SurfaceColor" Color="#ffffff"/>
        <SolidColorBrush x:Key="TextPrimaryColor" Color="#1e293b"/>
        <SolidColorBrush x:Key="TextSecondaryColor" Color="#64748b"/>

        <!-- أنماط الأزرار -->
        <Style x:Key="PrimaryButtonStyle" TargetType="Button">
            <Setter Property="Background" Value="{StaticResource PrimaryColor}"/>
            <Setter Property="Foreground" Value="White"/>
            <Setter Property="BorderThickness" Value="0"/>
            <Setter Property="Padding" Value="16,8"/>
            <Setter Property="Margin" Value="4"/>
            <Setter Property="FontWeight" Value="SemiBold"/>
            <Setter Property="Cursor" Value="Hand"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Border Background="{TemplateBinding Background}"
                                CornerRadius="8"
                                Padding="{TemplateBinding Padding}">
                            <ContentPresenter HorizontalAlignment="Center"
                                            VerticalAlignment="Center"/>
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter Property="Background" Value="{StaticResource PrimaryLightColor}"/>
                            </Trigger>
                            <Trigger Property="IsPressed" Value="True">
                                <Setter Property="Background" Value="{StaticResource PrimaryColor}"/>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>

        <!-- أنماط البطاقات -->
        <Style x:Key="CardStyle" TargetType="Border">
            <Setter Property="Background" Value="{StaticResource SurfaceColor}"/>
            <Setter Property="CornerRadius" Value="12"/>
            <Setter Property="Padding" Value="20"/>
            <Setter Property="Margin" Value="8"/>
            <Setter Property="Effect">
                <Setter.Value>
                    <DropShadowEffect Color="Gray" Direction="270" ShadowDepth="2" Opacity="0.1" BlurRadius="8"/>
                </Setter.Value>
            </Setter>
        </Style>

        <!-- أنماط النصوص -->
        <Style x:Key="HeaderTextStyle" TargetType="TextBlock">
            <Setter Property="FontSize" Value="24"/>
            <Setter Property="FontWeight" Value="Bold"/>
            <Setter Property="Foreground" Value="{StaticResource TextPrimaryColor}"/>
            <Setter Property="Margin" Value="0,0,0,16"/>
        </Style>

        <Style x:Key="SubHeaderTextStyle" TargetType="TextBlock">
            <Setter Property="FontSize" Value="18"/>
            <Setter Property="FontWeight" Value="SemiBold"/>
            <Setter Property="Foreground" Value="{StaticResource PrimaryColor}"/>
            <Setter Property="Margin" Value="0,0,0,8"/>
        </Style>
    </Window.Resources>

    <Grid Background="{StaticResource BackgroundColor}">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- شريط العنوان والقائمة العلوية -->
        <Border Grid.Row="0" Background="{StaticResource PrimaryColor}" Padding="20,16">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <!-- العنوان والشعار -->
                <StackPanel Grid.Column="0" Orientation="Horizontal">
                    <TextBlock Text="🏢" FontSize="32" VerticalAlignment="Center" Margin="0,0,16,0"/>
                    <StackPanel>
                        <TextBlock Text="نظام إدارة الموردين"
                                 FontSize="24" FontWeight="Bold"
                                 Foreground="White"/>
                        <TextBlock Text="جمعية المنقف التعاونية"
                                 FontSize="14"
                                 Foreground="White" Opacity="0.9"/>
                    </StackPanel>
                </StackPanel>

                <!-- أزرار القائمة العلوية -->
                <StackPanel Grid.Column="1" Orientation="Horizontal">
                    <Button x:Name="BtnDashboard" Content="🏠 الرئيسية" Style="{StaticResource PrimaryButtonStyle}"
                            Background="Transparent" Click="BtnDashboard_Click"/>
                    <Button x:Name="BtnSuppliers" Content="👥 الموردين" Style="{StaticResource PrimaryButtonStyle}"
                            Background="Transparent" Click="BtnSuppliers_Click"/>
                    <Button x:Name="BtnAccreditation" Content="✅ الاعتماد" Style="{StaticResource PrimaryButtonStyle}"
                            Background="Transparent" Click="BtnAccreditation_Click"/>
                    <Button x:Name="BtnReports" Content="📊 التقارير" Style="{StaticResource PrimaryButtonStyle}"
                            Background="Transparent" Click="BtnReports_Click"/>
                    <Button x:Name="BtnSettings" Content="⚙️ الإعدادات" Style="{StaticResource PrimaryButtonStyle}"
                            Background="Transparent" Click="BtnSettings_Click"/>
                </StackPanel>
            </Grid>
        </Border>

        <!-- المحتوى الرئيسي -->
        <ScrollViewer Grid.Row="1" VerticalScrollBarVisibility="Auto" Padding="20">
            <Grid x:Name="MainContent">
                <!-- لوحة المعلومات الرئيسية -->
                <StackPanel x:Name="DashboardPanel" Visibility="Visible">

                    <!-- بطاقات الإحصائيات -->
                    <TextBlock Text="📊 نظرة عامة على النظام" Style="{StaticResource HeaderTextStyle}"/>

                    <Grid Margin="0,0,0,32">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="*"/>
                        </Grid.ColumnDefinitions>

                        <!-- إجمالي الموردين -->
                        <Border Grid.Column="0" Style="{StaticResource CardStyle}">
                            <StackPanel>
                                <TextBlock Text="👥" FontSize="32" HorizontalAlignment="Center" Margin="0,0,0,8"/>
                                <TextBlock x:Name="TotalSuppliersText" Text="150" FontSize="28" FontWeight="Bold"
                                         Foreground="{StaticResource PrimaryColor}" HorizontalAlignment="Center"/>
                                <TextBlock Text="إجمالي الموردين" FontSize="14"
                                         Foreground="{StaticResource TextSecondaryColor}" HorizontalAlignment="Center"/>
                            </StackPanel>
                        </Border>

                        <!-- الموردين المعتمدين -->
                        <Border Grid.Column="1" Style="{StaticResource CardStyle}">
                            <StackPanel>
                                <TextBlock Text="✅" FontSize="32" HorizontalAlignment="Center" Margin="0,0,0,8"/>
                                <TextBlock x:Name="ApprovedSuppliersText" Text="112" FontSize="28" FontWeight="Bold"
                                         Foreground="{StaticResource SuccessColor}" HorizontalAlignment="Center"/>
                                <TextBlock Text="موردين معتمدين" FontSize="14"
                                         Foreground="{StaticResource TextSecondaryColor}" HorizontalAlignment="Center"/>
                            </StackPanel>
                        </Border>

                        <!-- قيد المراجعة -->
                        <Border Grid.Column="2" Style="{StaticResource CardStyle}">
                            <StackPanel>
                                <TextBlock Text="⏳" FontSize="32" HorizontalAlignment="Center" Margin="0,0,0,8"/>
                                <TextBlock x:Name="PendingSuppliersText" Text="23" FontSize="28" FontWeight="Bold"
                                         Foreground="{StaticResource WarningColor}" HorizontalAlignment="Center"/>
                                <TextBlock Text="قيد المراجعة" FontSize="14"
                                         Foreground="{StaticResource TextSecondaryColor}" HorizontalAlignment="Center"/>
                            </StackPanel>
                        </Border>

                        <!-- وثائق تنتهي قريباً -->
                        <Border Grid.Column="3" Style="{StaticResource CardStyle}">
                            <StackPanel>
                                <TextBlock Text="⚠️" FontSize="32" HorizontalAlignment="Center" Margin="0,0,0,8"/>
                                <TextBlock x:Name="ExpiringDocumentsText" Text="12" FontSize="28" FontWeight="Bold"
                                         Foreground="{StaticResource ErrorColor}" HorizontalAlignment="Center"/>
                                <TextBlock Text="وثائق تنتهي قريباً" FontSize="14"
                                         Foreground="{StaticResource TextSecondaryColor}" HorizontalAlignment="Center"/>
                            </StackPanel>
                        </Border>
                    </Grid>

                    <!-- الإجراءات السريعة -->
                    <TextBlock Text="⚡ الإجراءات السريعة" Style="{StaticResource HeaderTextStyle}"/>

                    <Grid Margin="0,0,0,32">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="*"/>
                        </Grid.ColumnDefinitions>

                        <Border Grid.Column="0" Style="{StaticResource CardStyle}">
                            <StackPanel>
                                <TextBlock Text="➕ إضافة مورد جديد" Style="{StaticResource SubHeaderTextStyle}"/>
                                <TextBlock Text="إضافة مورد جديد إلى النظام مع كافة البيانات والوثائق المطلوبة"
                                         TextWrapping="Wrap" Margin="0,0,0,16"
                                         Foreground="{StaticResource TextSecondaryColor}"/>
                                <Button x:Name="BtnAddSupplier" Content="إضافة مورد"
                                      Style="{StaticResource PrimaryButtonStyle}"
                                      HorizontalAlignment="Stretch" Click="BtnAddSupplier_Click"/>
                            </StackPanel>
                        </Border>

                        <Border Grid.Column="1" Style="{StaticResource CardStyle}">
                            <StackPanel>
                                <TextBlock Text="🔍 البحث في الموردين" Style="{StaticResource SubHeaderTextStyle}"/>
                                <TextBlock Text="البحث والفلترة في قائمة الموردين حسب الاسم أو الحالة أو نوع النشاط"
                                         TextWrapping="Wrap" Margin="0,0,0,16"
                                         Foreground="{StaticResource TextSecondaryColor}"/>
                                <Button x:Name="BtnSearchSuppliers" Content="البحث في الموردين"
                                      Style="{StaticResource PrimaryButtonStyle}"
                                      HorizontalAlignment="Stretch" Click="BtnSearchSuppliers_Click"/>
                            </StackPanel>
                        </Border>

                        <Border Grid.Column="2" Style="{StaticResource CardStyle}">
                            <StackPanel>
                                <TextBlock Text="📋 مراجعة الطلبات" Style="{StaticResource SubHeaderTextStyle}"/>
                                <TextBlock Text="مراجعة واعتماد طلبات الموردين الجدد والتحديثات المطلوبة"
                                         TextWrapping="Wrap" Margin="0,0,0,16"
                                         Foreground="{StaticResource TextSecondaryColor}"/>
                                <Button x:Name="BtnReviewRequests" Content="مراجعة الطلبات"
                                      Style="{StaticResource PrimaryButtonStyle}"
                                      HorizontalAlignment="Stretch" Click="BtnReviewRequests_Click"/>
                            </StackPanel>
                        </Border>
                    </Grid>

                    <!-- آخر النشاطات -->
                    <TextBlock Text="📝 آخر النشاطات" Style="{StaticResource HeaderTextStyle}"/>

                    <Border Style="{StaticResource CardStyle}">
                        <StackPanel>
                            <ListView x:Name="RecentActivitiesListView" Height="200" BorderThickness="0">
                                <ListView.View>
                                    <GridView>
                                        <GridViewColumn Header="النشاط" Width="200" DisplayMemberBinding="{Binding ActivityType}"/>
                                        <GridViewColumn Header="الوصف" Width="300" DisplayMemberBinding="{Binding ActivityDescription}"/>
                                        <GridViewColumn Header="المستخدم" Width="150" DisplayMemberBinding="{Binding PerformedBy}"/>
                                        <GridViewColumn Header="التاريخ" Width="150" DisplayMemberBinding="{Binding PerformedDate, StringFormat='{}{0:yyyy/MM/dd HH:mm}'}"/>
                                    </GridView>
                                </ListView.View>
                            </ListView>
                        </StackPanel>
                    </Border>

                </StackPanel>

                <!-- صفحة إدارة الموردين -->
                <StackPanel x:Name="SuppliersPanel" Visibility="Collapsed">
                    <TextBlock Text="👥 إدارة الموردين" Style="{StaticResource HeaderTextStyle}"/>

                    <!-- شريط البحث والفلترة -->
                    <Border Style="{StaticResource CardStyle}" Margin="0,0,0,20">
                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="200"/>
                                <ColumnDefinition Width="200"/>
                                <ColumnDefinition Width="Auto"/>
                            </Grid.ColumnDefinitions>

                            <TextBox x:Name="TxtSearchSuppliers" Grid.Column="0"
                                   Style="{StaticResource InputTextBoxStyle}"
                                   Margin="0,0,10,0"
                                   Text="🔍 البحث في الموردين..."
                                   GotFocus="TxtSearchSuppliers_GotFocus"
                                   LostFocus="TxtSearchSuppliers_LostFocus"
                                   TextChanged="TxtSearchSuppliers_TextChanged"/>

                            <ComboBox x:Name="CmbStatusFilter" Grid.Column="1"
                                    Style="{StaticResource InputComboBoxStyle}"
                                    Margin="0,0,10,0"
                                    SelectionChanged="CmbStatusFilter_SelectionChanged">
                                <ComboBoxItem Content="جميع الحالات" IsSelected="True"/>
                                <ComboBoxItem Content="جديد"/>
                                <ComboBoxItem Content="قيد المراجعة"/>
                                <ComboBoxItem Content="معتمد"/>
                                <ComboBoxItem Content="مرفوض"/>
                                <ComboBoxItem Content="معلق"/>
                            </ComboBox>

                            <ComboBox x:Name="CmbCategoryFilter" Grid.Column="2"
                                    Style="{StaticResource InputComboBoxStyle}"
                                    Margin="0,0,10,0"
                                    SelectionChanged="CmbCategoryFilter_SelectionChanged"/>

                            <Button x:Name="BtnAddNewSupplier" Grid.Column="3"
                                  Content="➕ إضافة مورد"
                                  Style="{StaticResource PrimaryButtonStyle}"
                                  Click="BtnAddNewSupplier_Click"/>
                        </Grid>
                    </Border>

                    <!-- قائمة الموردين -->
                    <Border Style="{StaticResource CardStyle}">
                        <StackPanel>
                            <TextBlock Text="📋 قائمة الموردين" Style="{StaticResource SubHeaderTextStyle}"/>

                            <DataGrid x:Name="SuppliersDataGrid"
                                    Height="400"
                                    AutoGenerateColumns="False"
                                    CanUserAddRows="False"
                                    CanUserDeleteRows="False"
                                    IsReadOnly="True"
                                    GridLinesVisibility="Horizontal"
                                    HeadersVisibility="Column"
                                    SelectionMode="Single"
                                    MouseDoubleClick="SuppliersDataGrid_MouseDoubleClick">
                                <DataGrid.Columns>
                                    <DataGridTextColumn Header="الاسم" Binding="{Binding SupplierName}" Width="200"/>
                                    <DataGridTextColumn Header="رقم الرخصة" Binding="{Binding LicenseNumber}" Width="150"/>
                                    <DataGridTextColumn Header="الشخص المسؤول" Binding="{Binding ContactPerson}" Width="150"/>
                                    <DataGridTextColumn Header="الهاتف" Binding="{Binding PhoneNumber}" Width="120"/>
                                    <DataGridTemplateColumn Header="الحالة" Width="120">
                                        <DataGridTemplateColumn.CellTemplate>
                                            <DataTemplate>
                                                <Border Background="{Binding StatusColor}"
                                                      CornerRadius="12"
                                                      Padding="8,4">
                                                    <TextBlock Text="{Binding Status}"
                                                             Foreground="White"
                                                             FontWeight="SemiBold"
                                                             HorizontalAlignment="Center"/>
                                                </Border>
                                            </DataTemplate>
                                        </DataGridTemplateColumn.CellTemplate>
                                    </DataGridTemplateColumn>
                                    <DataGridTextColumn Header="تاريخ الإنشاء" Binding="{Binding CreatedDate, StringFormat='{}{0:yyyy/MM/dd}'}" Width="120"/>
                                    <DataGridTemplateColumn Header="الإجراءات" Width="150">
                                        <DataGridTemplateColumn.CellTemplate>
                                            <DataTemplate>
                                                <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                                                    <Button Content="👁️" ToolTip="عرض"
                                                          Style="{StaticResource PrimaryButtonStyle}"
                                                          Margin="2" Padding="8,4"
                                                          Click="BtnViewSupplier_Click"/>
                                                    <Button Content="✏️" ToolTip="تحرير"
                                                          Style="{StaticResource PrimaryButtonStyle}"
                                                          Margin="2" Padding="8,4"
                                                          Click="BtnEditSupplier_Click"/>
                                                    <Button Content="🗑️" ToolTip="حذف"
                                                          Background="#ef4444"
                                                          Foreground="White"
                                                          BorderThickness="0"
                                                          Margin="2" Padding="8,4"
                                                          Click="BtnDeleteSupplier_Click"/>
                                                </StackPanel>
                                            </DataTemplate>
                                        </DataGridTemplateColumn.CellTemplate>
                                    </DataGridTemplateColumn>
                                </DataGrid.Columns>
                            </DataGrid>

                            <!-- معلومات الصفحة -->
                            <StackPanel Orientation="Horizontal" HorizontalAlignment="Center" Margin="0,10,0,0">
                                <TextBlock x:Name="TxtSuppliersCount" Text="إجمالي الموردين: 0"
                                         Foreground="{StaticResource TextSecondaryColor}"/>
                            </StackPanel>
                        </StackPanel>
                    </Border>
                </StackPanel>

                <!-- صفحة اعتماد الموردين -->
                <StackPanel x:Name="AccreditationPanel" Visibility="Collapsed">
                    <TextBlock Text="✅ اعتماد الموردين" Style="{StaticResource HeaderTextStyle}"/>

                    <!-- إحصائيات سريعة -->
                    <Grid Margin="0,0,0,20">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="*"/>
                        </Grid.ColumnDefinitions>

                        <Border Grid.Column="0" Style="{StaticResource CardStyle}">
                            <StackPanel>
                                <TextBlock Text="⏳" FontSize="32" HorizontalAlignment="Center"/>
                                <TextBlock x:Name="TxtPendingCount" Text="0" FontSize="24" FontWeight="Bold"
                                         Foreground="{StaticResource WarningColor}" HorizontalAlignment="Center"/>
                                <TextBlock Text="في انتظار المراجعة" FontSize="12"
                                         Foreground="{StaticResource TextSecondaryColor}" HorizontalAlignment="Center"/>
                            </StackPanel>
                        </Border>

                        <Border Grid.Column="1" Style="{StaticResource CardStyle}">
                            <StackPanel>
                                <TextBlock Text="📋" FontSize="32" HorizontalAlignment="Center"/>
                                <TextBlock x:Name="TxtTodayReviews" Text="0" FontSize="24" FontWeight="Bold"
                                         Foreground="{StaticResource AccentColor}" HorizontalAlignment="Center"/>
                                <TextBlock Text="مراجعات اليوم" FontSize="12"
                                         Foreground="{StaticResource TextSecondaryColor}" HorizontalAlignment="Center"/>
                            </StackPanel>
                        </Border>

                        <Border Grid.Column="2" Style="{StaticResource CardStyle}">
                            <StackPanel>
                                <TextBlock Text="⚠️" FontSize="32" HorizontalAlignment="Center"/>
                                <TextBlock x:Name="TxtUrgentReviews" Text="0" FontSize="24" FontWeight="Bold"
                                         Foreground="{StaticResource ErrorColor}" HorizontalAlignment="Center"/>
                                <TextBlock Text="مراجعات عاجلة" FontSize="12"
                                         Foreground="{StaticResource TextSecondaryColor}" HorizontalAlignment="Center"/>
                            </StackPanel>
                        </Border>
                    </Grid>

                    <!-- قائمة الموردين للمراجعة -->
                    <Border Style="{StaticResource CardStyle}">
                        <StackPanel>
                            <Grid>
                                <TextBlock Text="📋 الموردين في انتظار المراجعة" Style="{StaticResource SubHeaderTextStyle}"/>
                                <Button x:Name="BtnRefreshAccreditation" Content="🔄 تحديث"
                                      Style="{StaticResource PrimaryButtonStyle}"
                                      HorizontalAlignment="Left"
                                      Click="BtnRefreshAccreditation_Click"/>
                            </Grid>

                            <DataGrid x:Name="AccreditationDataGrid"
                                    Height="350"
                                    AutoGenerateColumns="False"
                                    CanUserAddRows="False"
                                    CanUserDeleteRows="False"
                                    IsReadOnly="True"
                                    GridLinesVisibility="Horizontal"
                                    HeadersVisibility="Column"
                                    SelectionMode="Single">
                                <DataGrid.Columns>
                                    <DataGridTextColumn Header="اسم المورد" Binding="{Binding SupplierName}" Width="200"/>
                                    <DataGridTextColumn Header="رقم الرخصة" Binding="{Binding LicenseNumber}" Width="150"/>
                                    <DataGridTextColumn Header="تاريخ التقديم" Binding="{Binding CreatedDate, StringFormat='{}{0:yyyy/MM/dd}'}" Width="120"/>
                                    <DataGridTextColumn Header="عدد الوثائق" Binding="{Binding DocumentCount}" Width="100"/>
                                    <DataGridTemplateColumn Header="الحالة" Width="120">
                                        <DataGridTemplateColumn.CellTemplate>
                                            <DataTemplate>
                                                <Border Background="{Binding StatusColor}"
                                                      CornerRadius="12"
                                                      Padding="8,4">
                                                    <TextBlock Text="{Binding Status}"
                                                             Foreground="White"
                                                             FontWeight="SemiBold"
                                                             HorizontalAlignment="Center"/>
                                                </Border>
                                            </DataTemplate>
                                        </DataGridTemplateColumn.CellTemplate>
                                    </DataGridTemplateColumn>
                                    <DataGridTemplateColumn Header="الإجراءات" Width="200">
                                        <DataGridTemplateColumn.CellTemplate>
                                            <DataTemplate>
                                                <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                                                    <Button Content="✅ اعتماد"
                                                          Background="{StaticResource SuccessColor}"
                                                          Foreground="White"
                                                          BorderThickness="0"
                                                          Margin="2" Padding="8,4"
                                                          Click="BtnApproveSupplier_Click"/>
                                                    <Button Content="❌ رفض"
                                                          Background="{StaticResource ErrorColor}"
                                                          Foreground="White"
                                                          BorderThickness="0"
                                                          Margin="2" Padding="8,4"
                                                          Click="BtnRejectSupplier_Click"/>
                                                    <Button Content="⏸️ تأجيل"
                                                          Background="{StaticResource WarningColor}"
                                                          Foreground="White"
                                                          BorderThickness="0"
                                                          Margin="2" Padding="8,4"
                                                          Click="BtnPostponeSupplier_Click"/>
                                                </StackPanel>
                                            </DataTemplate>
                                        </DataGridTemplateColumn.CellTemplate>
                                    </DataGridTemplateColumn>
                                </DataGrid.Columns>
                            </DataGrid>
                        </StackPanel>
                    </Border>
                </StackPanel>

                <!-- صفحة التقارير والإحصائيات -->
                <StackPanel x:Name="ReportsPanel" Visibility="Collapsed">
                    <TextBlock Text="📊 التقارير والإحصائيات" Style="{StaticResource HeaderTextStyle}"/>

                    <!-- أنواع التقارير -->
                    <Grid Margin="0,0,0,20">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="*"/>
                        </Grid.ColumnDefinitions>

                        <Border Grid.Column="0" Style="{StaticResource CardStyle}">
                            <StackPanel>
                                <TextBlock Text="📈 التقارير السريعة" Style="{StaticResource SubHeaderTextStyle}"/>

                                <Button x:Name="BtnSuppliersStatusReport" Content="📊 تقرير حالة الموردين"
                                      Style="{StaticResource PrimaryButtonStyle}"
                                      HorizontalAlignment="Stretch" Margin="0,5"
                                      Click="BtnSuppliersStatusReport_Click"/>

                                <Button x:Name="BtnExpiringDocumentsReport" Content="⚠️ تقرير الوثائق المنتهية"
                                      Style="{StaticResource PrimaryButtonStyle}"
                                      HorizontalAlignment="Stretch" Margin="0,5"
                                      Click="BtnExpiringDocumentsReport_Click"/>

                                <Button x:Name="BtnActivitiesReport" Content="📝 تقرير النشاطات"
                                      Style="{StaticResource PrimaryButtonStyle}"
                                      HorizontalAlignment="Stretch" Margin="0,5"
                                      Click="BtnActivitiesReport_Click"/>

                                <Button x:Name="BtnCategoriesReport" Content="🏷️ تقرير الفئات"
                                      Style="{StaticResource PrimaryButtonStyle}"
                                      HorizontalAlignment="Stretch" Margin="0,5"
                                      Click="BtnCategoriesReport_Click"/>
                            </StackPanel>
                        </Border>

                        <Border Grid.Column="1" Style="{StaticResource CardStyle}">
                            <StackPanel>
                                <TextBlock Text="🎯 إحصائيات مفصلة" Style="{StaticResource SubHeaderTextStyle}"/>

                                <!-- إحصائيات الحالات -->
                                <TextBlock Text="توزيع الموردين حسب الحالة:" FontWeight="SemiBold" Margin="0,10,0,5"/>
                                <StackPanel x:Name="StatusStatisticsPanel">
                                    <!-- سيتم ملؤها ديناميكياً -->
                                </StackPanel>

                                <!-- إحصائيات الفئات -->
                                <TextBlock Text="توزيع الموردين حسب الفئة:" FontWeight="SemiBold" Margin="0,15,0,5"/>
                                <StackPanel x:Name="CategoryStatisticsPanel">
                                    <!-- سيتم ملؤها ديناميكياً -->
                                </StackPanel>
                            </StackPanel>
                        </Border>
                    </Grid>

                    <!-- منطقة عرض التقارير -->
                    <Border Style="{StaticResource CardStyle}">
                        <StackPanel>
                            <Grid>
                                <TextBlock x:Name="TxtReportTitle" Text="📋 منطقة عرض التقارير" Style="{StaticResource SubHeaderTextStyle}"/>
                                <StackPanel Orientation="Horizontal" HorizontalAlignment="Left">
                                    <Button x:Name="BtnPrintReport" Content="🖨️ طباعة"
                                          Style="{StaticResource PrimaryButtonStyle}"
                                          Click="BtnPrintReport_Click"/>
                                    <Button x:Name="BtnExportReport" Content="📥 تصدير"
                                          Style="{StaticResource PrimaryButtonStyle}"
                                          Click="BtnExportReport_Click"/>
                                </StackPanel>
                            </Grid>

                            <!-- منطقة المحتوى -->
                            <ScrollViewer x:Name="ReportContentScrollViewer" Height="400" VerticalScrollBarVisibility="Auto">
                                <StackPanel x:Name="ReportContentPanel">
                                    <TextBlock Text="اختر نوع التقرير من القائمة أعلاه لعرض المحتوى هنا"
                                             HorizontalAlignment="Center"
                                             VerticalAlignment="Center"
                                             Foreground="{StaticResource TextSecondaryColor}"
                                             FontSize="16"
                                             Margin="50"/>
                                </StackPanel>
                            </ScrollViewer>
                        </StackPanel>
                    </Border>
                </StackPanel>

                <!-- صفحة الإعدادات -->
                <StackPanel x:Name="SettingsPanel" Visibility="Collapsed">
                    <TextBlock Text="⚙️ إعدادات النظام" Style="{StaticResource HeaderTextStyle}"/>

                    <Grid>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="*"/>
                        </Grid.ColumnDefinitions>

                        <!-- إعدادات النظام -->
                        <Border Grid.Column="0" Style="{StaticResource CardStyle}">
                            <StackPanel>
                                <TextBlock Text="🔧 إعدادات النظام" Style="{StaticResource SubHeaderTextStyle}"/>

                                <TextBlock Text="اسم التطبيق:" Style="{StaticResource LabelStyle}"/>
                                <TextBox x:Name="TxtApplicationName"
                                       Text="نظام إدارة الموردين - جمعية المنقف التعاونية"
                                       Style="{StaticResource InputTextBoxStyle}"/>

                                <TextBlock Text="اسم الشركة:" Style="{StaticResource LabelStyle}"/>
                                <TextBox x:Name="TxtCompanyName"
                                       Text="جمعية المنقف التعاونية"
                                       Style="{StaticResource InputTextBoxStyle}"/>

                                <TextBlock Text="فترة التنبيه للوثائق (بالأيام):" Style="{StaticResource LabelStyle}"/>
                                <TextBox x:Name="TxtDocumentWarningDays"
                                       Text="30"
                                       Style="{StaticResource InputTextBoxStyle}"/>

                                <CheckBox x:Name="ChkAutoBackup"
                                        Content="تفعيل النسخ الاحتياطي التلقائي"
                                        IsChecked="True"
                                        Margin="0,10"/>

                                <CheckBox x:Name="ChkShowNotifications"
                                        Content="إظهار التنبيهات"
                                        IsChecked="True"
                                        Margin="0,5"/>
                            </StackPanel>
                        </Border>

                        <!-- إعدادات قاعدة البيانات -->
                        <Border Grid.Column="1" Style="{StaticResource CardStyle}">
                            <StackPanel>
                                <TextBlock Text="🗄️ إعدادات قاعدة البيانات" Style="{StaticResource SubHeaderTextStyle}"/>

                                <TextBlock Text="مسار قاعدة البيانات:" Style="{StaticResource LabelStyle}"/>
                                <Grid>
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="*"/>
                                        <ColumnDefinition Width="Auto"/>
                                    </Grid.ColumnDefinitions>
                                    <TextBox x:Name="TxtDatabasePath"
                                           Text="SupplierManagement.db"
                                           Style="{StaticResource InputTextBoxStyle}"
                                           IsReadOnly="True"/>
                                    <Button Grid.Column="1" Content="📁"
                                          Style="{StaticResource PrimaryButtonStyle}"
                                          Click="BtnBrowseDatabasePath_Click"/>
                                </Grid>

                                <TextBlock Text="حجم قاعدة البيانات:" Style="{StaticResource LabelStyle}"/>
                                <TextBlock x:Name="TxtDatabaseSize" Text="حساب الحجم..."
                                         Foreground="{StaticResource TextSecondaryColor}"/>

                                <Button x:Name="BtnBackupDatabase" Content="💾 إنشاء نسخة احتياطية"
                                      Style="{StaticResource PrimaryButtonStyle}"
                                      HorizontalAlignment="Stretch"
                                      Margin="0,10,0,5"
                                      Click="BtnBackupDatabase_Click"/>

                                <Button x:Name="BtnRestoreDatabase" Content="📥 استعادة نسخة احتياطية"
                                      Style="{StaticResource SecondaryButtonStyle}"
                                      HorizontalAlignment="Stretch"
                                      Margin="0,5"
                                      Click="BtnRestoreDatabase_Click"/>

                                <Button x:Name="BtnOptimizeDatabase" Content="⚡ تحسين قاعدة البيانات"
                                      Style="{StaticResource PrimaryButtonStyle}"
                                      HorizontalAlignment="Stretch"
                                      Margin="0,5"
                                      Click="BtnOptimizeDatabase_Click"/>
                            </StackPanel>
                        </Border>
                    </Grid>

                    <!-- أزرار الحفظ -->
                    <Border Style="{StaticResource CardStyle}" Margin="0,20,0,0">
                        <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                            <Button x:Name="BtnSaveSettings" Content="💾 حفظ الإعدادات"
                                  Style="{StaticResource PrimaryButtonStyle}"
                                  Click="BtnSaveSettings_Click"/>
                            <Button x:Name="BtnResetSettings" Content="🔄 إعادة تعيين"
                                  Style="{StaticResource SecondaryButtonStyle}"
                                  Click="BtnResetSettings_Click"/>
                        </StackPanel>
                    </Border>
                </StackPanel>

            </Grid>
        </ScrollViewer>

        <!-- شريط الحالة السفلي -->
        <Border Grid.Row="2" Background="{StaticResource PrimaryColor}" Padding="20,8">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <TextBlock Grid.Column="0" Text="© 2024 جمعية المنقف التعاونية - نظام إدارة الموردين"
                         Foreground="White" VerticalAlignment="Center"/>

                <StackPanel Grid.Column="1" Orientation="Horizontal">
                    <TextBlock x:Name="StatusText" Text="متصل" Foreground="White" VerticalAlignment="Center" Margin="0,0,16,0"/>
                    <TextBlock x:Name="CurrentTimeText" Text="" Foreground="White" VerticalAlignment="Center"/>
                </StackPanel>
            </Grid>
        </Border>

    </Grid>
</Window>