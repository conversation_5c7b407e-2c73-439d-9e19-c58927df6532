# نظام إدارة الموردين - جمعية المنقف التعاونية
## Supplier Management System - Al-Mangaf Cooperative Society

![.NET](https://img.shields.io/badge/.NET-8.0-blue)
![WPF](https://img.shields.io/badge/WPF-Windows-lightblue)
![SQLite](https://img.shields.io/badge/Database-SQLite-green)
![Entity Framework](https://img.shields.io/badge/ORM-Entity%20Framework-orange)

## 📋 نظرة عامة

نظام إدارة الموردين هو تطبيق سطح مكتب متطور مصمم خصيصاً لجمعية المنقف التعاونية لإدارة واعتماد الموردين بطريقة احترافية ومنظمة. يوفر النظام واجهة سهلة الاستخدام باللغة العربية مع جميع الميزات المطلوبة للعمل الفعلي.

## ✨ الميزات الرئيسية

### 🏠 لوحة المعلومات الرئيسية
- عرض إحصائيات شاملة للموردين
- مراقبة حالات الاعتماد في الوقت الفعلي
- تتبع الوثائق المنتهية الصلاحية
- عرض آخر النشاطات في النظام

### 👥 إدارة الموردين
- إضافة موردين جدد مع التحقق من صحة البيانات
- تحديث معلومات الموردين الحالية
- تصنيف الموردين حسب الفئات
- منع تكرار أرقام الرخص التجارية
- تتبع حالات الموردين (جديد، قيد المراجعة، معتمد، مرفوض، معلق)

### 📄 إدارة الوثائق
- رفع وحفظ وثائق الموردين
- تتبع تواريخ انتهاء الوثائق
- تصنيف الوثائق حسب النوع
- تنبيهات للوثائق المنتهية الصلاحية

### ✅ نظام الاعتماد
- مراجعة طلبات الموردين الجدد
- اعتماد أو رفض الموردين
- تسجيل قرارات لجنة المشتريات
- تتبع تاريخ جميع القرارات

### 📊 التقارير والإحصائيات
- تقارير شاملة عن حالة الموردين
- إحصائيات مرئية وتفاعلية
- تصدير البيانات والتقارير
- رسوم بيانية للإحصائيات

## 🛠️ التقنيات المستخدمة

- **Framework**: .NET 8.0
- **UI Framework**: WPF (Windows Presentation Foundation)
- **Database**: SQLite
- **ORM**: Entity Framework Core
- **Language**: C#
- **Architecture**: MVVM Pattern

## 📦 المتطلبات

- Windows 10 أو أحدث
- .NET 8.0 Runtime
- 100 MB مساحة فارغة على القرص الصلب
- 2 GB RAM (الحد الأدنى)

## 🚀 التثبيت والتشغيل

### 1. تحميل المشروع
```bash
git clone [repository-url]
cd SupplierManagementSystem
```

### 2. استعادة الحزم
```bash
dotnet restore
```

### 3. بناء المشروع
```bash
dotnet build
```

### 4. تشغيل التطبيق
```bash
dotnet run
```

## 📁 هيكل المشروع

```
SupplierManagementSystem/
├── Models/                 # نماذج البيانات
│   ├── Supplier.cs        # نموذج المورد
│   ├── SupplierDocument.cs # نموذج وثائق المورد
│   ├── DocumentType.cs    # نموذج أنواع الوثائق
│   ├── AccreditationRecord.cs # نموذج سجلات الاعتماد
│   ├── ProcurementCommitteeMember.cs # نموذج أعضاء لجنة المشتريات
│   ├── SupplierEvaluation.cs # نموذج تقييم الموردين
│   ├── SupplierCategory.cs # نموذج فئات الموردين
│   └── ActivityLog.cs     # نموذج سجل النشاطات
├── Data/                  # طبقة البيانات
│   └── SupplierDbContext.cs # سياق قاعدة البيانات
├── Services/              # طبقة الخدمات
│   └── SupplierService.cs # خدمة إدارة الموردين
├── Views/                 # النوافذ والواجهات
│   └── AddSupplierWindow.xaml # نافذة إضافة مورد
├── Database/              # سكريبتات قاعدة البيانات
│   └── CreateDatabase.sql # سكريبت إنشاء قاعدة البيانات
├── MainWindow.xaml        # النافذة الرئيسية
├── appsettings.json       # إعدادات التطبيق
└── README.md             # هذا الملف
```

## 🗄️ قاعدة البيانات

يستخدم النظام قاعدة بيانات SQLite محلية تحتوي على الجداول التالية:

- **Suppliers**: معلومات الموردين الأساسية
- **DocumentTypes**: أنواع الوثائق المطلوبة
- **SupplierDocuments**: وثائق الموردين
- **ProcurementCommittee**: أعضاء لجنة المشتريات
- **AccreditationRecords**: سجلات الاعتماد والقرارات
- **SupplierEvaluations**: تقييمات الموردين
- **SupplierCategories**: فئات الموردين
- **SupplierCategoryMapping**: ربط الموردين بالفئات
- **ActivityLog**: سجل جميع النشاطات

## 🔧 الإعدادات

يمكن تخصيص النظام من خلال ملف `appsettings.json`:

```json
{
  "ConnectionStrings": {
    "DefaultConnection": "Data Source=SupplierManagement.db"
  },
  "ApplicationSettings": {
    "ApplicationName": "نظام إدارة الموردين - جمعية المنقف التعاونية",
    "Version": "1.0.0",
    "CompanyName": "جمعية المنقف التعاونية"
  }
}
```

## 📝 كيفية الاستخدام

### إضافة مورد جديد
1. انقر على "إضافة مورد" من الصفحة الرئيسية
2. املأ المعلومات المطلوبة
3. اختر فئة المورد
4. انقر "حفظ المورد"

### مراجعة الموردين
1. انتقل إلى قسم "الاعتماد"
2. اختر المورد المراد مراجعته
3. اتخذ القرار المناسب (موافقة/رفض/تأجيل)
4. أضف الملاحظات المطلوبة

### عرض التقارير
1. انتقل إلى قسم "التقارير"
2. اختر نوع التقرير المطلوب
3. حدد الفترة الزمنية
4. انقر "إنشاء التقرير"

## 🔒 الأمان

- التحقق من صحة البيانات على مستويات متعددة
- حماية من SQL Injection باستخدام Entity Framework
- سجل كامل لجميع العمليات والتغييرات
- نسخ احتياطية تلقائية للبيانات

## 🐛 الإبلاغ عن المشاكل

إذا واجهت أي مشاكل أو أخطاء، يرجى:
1. التحقق من ملف السجل في مجلد `Logs`
2. إرسال تقرير مفصل عن المشكلة
3. تضمين رسالة الخطأ إن وجدت

## 📞 الدعم الفني

للحصول على الدعم الفني:
- البريد الإلكتروني: <EMAIL>
- الهاتف: +965 2398 xxxx

## 📄 الترخيص

هذا المشروع مطور خصيصاً لجمعية المنقف التعاونية.
جميع الحقوق محفوظة © 2024

## 🙏 شكر وتقدير

تم تطوير هذا النظام بعناية فائقة ليلبي احتياجات جمعية المنقف التعاونية في إدارة الموردين بطريقة احترافية ومنظمة.

---

**نظام إدارة الموردين - جمعية المنقف التعاونية**  
**الإصدار 1.0.0**  
**تاريخ الإصدار: 2024**
