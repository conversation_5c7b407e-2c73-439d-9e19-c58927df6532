using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;

namespace SupplierManagementSystem.Models
{
    /// <summary>
    /// نموذج بيانات فئة المورد
    /// Supplier Category Data Model
    /// </summary>
    public class SupplierCategory
    {
        [Key]
        public int CategoryID { get; set; }

        [Required(ErrorMessage = "اسم الفئة مطلوب")]
        [StringLength(100, ErrorMessage = "اسم الفئة يجب أن يكون أقل من 100 حرف")]
        [Display(Name = "اسم الفئة")]
        public string CategoryName { get; set; } = string.Empty;

        [StringLength(500, ErrorMessage = "الوصف يجب أن يكون أقل من 500 حرف")]
        [Display(Name = "الوصف")]
        public string? Description { get; set; }

        [Display(Name = "نشط")]
        public bool IsActive { get; set; } = true;

        // Navigation Properties
        public virtual ICollection<SupplierCategoryMapping> SupplierMappings { get; set; } = new List<SupplierCategoryMapping>();

        // Methods
        public override string ToString()
        {
            return CategoryName;
        }
    }

    /// <summary>
    /// نموذج بيانات ربط المورد بالفئة
    /// Supplier Category Mapping Data Model
    /// </summary>
    public class SupplierCategoryMapping
    {
        [Key]
        public int MappingID { get; set; }

        [Required]
        [Display(Name = "معرف المورد")]
        public int SupplierID { get; set; }

        [Required]
        [Display(Name = "معرف الفئة")]
        public int CategoryID { get; set; }

        [Display(Name = "تاريخ التعيين")]
        public DateTime AssignedDate { get; set; } = DateTime.Now;

        [StringLength(100)]
        [Display(Name = "عُين بواسطة")]
        public string? AssignedBy { get; set; }

        [Display(Name = "نشط")]
        public bool IsActive { get; set; } = true;

        // Navigation Properties
        public virtual Supplier? Supplier { get; set; }
        public virtual SupplierCategory? Category { get; set; }

        // Methods
        public override string ToString()
        {
            return $"{Supplier?.SupplierName} - {Category?.CategoryName}";
        }
    }
}
