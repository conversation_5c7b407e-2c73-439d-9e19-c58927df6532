{"ConnectionStrings": {"DefaultConnection": "Data Source=SupplierManagement.db", "SqlServerConnection": "Server=.;Database=SupplierManagementDB;Integrated Security=true;MultipleActiveResultSets=true;TrustServerCertificate=true"}, "ApplicationSettings": {"ApplicationName": "نظام إدارة الموردين - جمعية المنقف التعاونية", "Version": "1.0.0", "CompanyName": "جمعية المنقف التعاونية", "SupportEmail": "<EMAIL>", "DocumentsPath": "Documents", "BackupPath": "Backups", "LogsPath": "Logs"}, "DocumentSettings": {"MaxFileSizeMB": 10, "AllowedExtensions": [".pdf", ".doc", ".docx", ".jpg", ".jpeg", ".png", ".tiff"], "DocumentExpiryWarningDays": 30, "AutoBackupEnabled": true, "BackupIntervalHours": 24}, "SecuritySettings": {"SessionTimeoutMinutes": 60, "MaxLoginAttempts": 3, "PasswordMinLength": 8, "RequirePasswordComplexity": true, "EnableAuditLog": true}, "NotificationSettings": {"EmailEnabled": true, "SMSEnabled": false, "NotifyOnDocumentExpiry": true, "NotifyOnStatusChange": true, "NotifyOnNewSupplier": true}, "ReportSettings": {"DefaultReportFormat": "PDF", "IncludeLogo": true, "ReportFooter": "جمعية المنقف التعاونية - نظام إدارة الموردين", "AutoGenerateReports": true, "ReportSchedule": "Monthly"}, "UISettings": {"Theme": "Light", "Language": "Arabic", "DateFormat": "dd/MM/yyyy", "CurrencyFormat": "KWD", "ItemsPerPage": 25, "EnableAnimations": true}}