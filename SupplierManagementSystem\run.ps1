# نظام إدارة الموردين - جمعية المنقف التعاونية
# Supplier Management System - Al-Mangaf Cooperative Society

Write-Host ""
Write-Host "🚀 تشغيل نظام إدارة الموردين - جمعية المنقف التعاونية" -ForegroundColor Green
Write-Host "================================================" -ForegroundColor Green
Write-Host ""

Write-Host "⏳ جاري تشغيل التطبيق..." -ForegroundColor Yellow
Write-Host ""

try {
    # محاولة التشغيل باستخدام dotnet run
    Write-Host "📋 الطريقة الأولى: dotnet run" -ForegroundColor Cyan
    $process = Start-Process -FilePath "dotnet" -ArgumentList "run" -PassThru -NoNewWindow
    
    # انتظار لمدة 5 ثوانٍ للتحقق من نجاح التشغيل
    Start-Sleep -Seconds 5
    
    if (!$process.HasExited) {
        Write-Host "✅ تم تشغيل التطبيق بنجاح!" -ForegroundColor Green
        Write-Host "🔍 ابحث عن نافذة التطبيق على شاشتك" -ForegroundColor Yellow
        Write-Host ""
        Write-Host "اضغط أي مفتاح للخروج من هذه النافذة..." -ForegroundColor Gray
        $null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
        exit 0
    }
}
catch {
    Write-Host "❌ فشل في الطريقة الأولى" -ForegroundColor Red
}

try {
    # محاولة التشغيل المباشر للملف التنفيذي
    Write-Host ""
    Write-Host "📋 الطريقة الثانية: تشغيل الملف التنفيذي مباشرة" -ForegroundColor Cyan
    
    $exePath = ".\bin\Debug\net8.0-windows\SupplierManagementSystem.exe"
    
    if (Test-Path $exePath) {
        $process = Start-Process -FilePath $exePath -PassThru
        
        Start-Sleep -Seconds 3
        
        if (!$process.HasExited) {
            Write-Host "✅ تم تشغيل التطبيق بنجاح!" -ForegroundColor Green
            Write-Host "🔍 ابحث عن نافذة التطبيق على شاشتك" -ForegroundColor Yellow
            Write-Host ""
            Write-Host "اضغط أي مفتاح للخروج من هذه النافذة..." -ForegroundColor Gray
            $null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
            exit 0
        }
    }
    else {
        Write-Host "❌ الملف التنفيذي غير موجود: $exePath" -ForegroundColor Red
    }
}
catch {
    Write-Host "❌ فشل في الطريقة الثانية" -ForegroundColor Red
}

# إذا فشلت جميع الطرق
Write-Host ""
Write-Host "❌ فشل في تشغيل التطبيق بجميع الطرق" -ForegroundColor Red
Write-Host ""
Write-Host "🔧 تحقق من:" -ForegroundColor Yellow
Write-Host "   1. تثبيت .NET 8.0 Runtime" -ForegroundColor White
Write-Host "   2. وجود جميع الملفات المطلوبة" -ForegroundColor White
Write-Host "   3. صلاحيات الوصول للمجلد" -ForegroundColor White
Write-Host "   4. إعدادات مكافح الفيروسات" -ForegroundColor White
Write-Host "   5. إعدادات Windows Defender" -ForegroundColor White
Write-Host ""
Write-Host "📞 للدعم الفني: <EMAIL>" -ForegroundColor Cyan
Write-Host ""
Write-Host "اضغط أي مفتاح للخروج..." -ForegroundColor Gray
$null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
