<Window x:Class="SupplierManagementSystem.Views.AddSupplierWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        mc:Ignorable="d"
        Title="إضافة مورد جديد - نظام إدارة الموردين" 
        Height="700" Width="800" 
        WindowStartupLocation="CenterOwner"
        FlowDirection="RightToLeft"
        FontFamily="Segoe UI, Tahoma"
        FontSize="14"
        ResizeMode="NoResize">
    
    <Window.Resources>
        <!-- تعريف الألوان والأنماط -->
        <SolidColorBrush x:Key="PrimaryColor" Color="#0f766e"/>
        <SolidColorBrush x:Key="PrimaryLightColor" Color="#14b8a6"/>
        <SolidColorBrush x:Key="SecondaryColor" Color="#f59e0b"/>
        <SolidColorBrush x:Key="AccentColor" Color="#3b82f6"/>
        <SolidColorBrush x:Key="SuccessColor" Color="#10b981"/>
        <SolidColorBrush x:Key="WarningColor" Color="#f59e0b"/>
        <SolidColorBrush x:Key="ErrorColor" Color="#ef4444"/>
        <SolidColorBrush x:Key="BackgroundColor" Color="#f8fafc"/>
        <SolidColorBrush x:Key="SurfaceColor" Color="#ffffff"/>
        <SolidColorBrush x:Key="TextPrimaryColor" Color="#1e293b"/>
        <SolidColorBrush x:Key="TextSecondaryColor" Color="#64748b"/>
        
        <!-- أنماط الأزرار -->
        <Style x:Key="PrimaryButtonStyle" TargetType="Button">
            <Setter Property="Background" Value="{StaticResource PrimaryColor}"/>
            <Setter Property="Foreground" Value="White"/>
            <Setter Property="BorderThickness" Value="0"/>
            <Setter Property="Padding" Value="16,8"/>
            <Setter Property="Margin" Value="4"/>
            <Setter Property="FontWeight" Value="SemiBold"/>
            <Setter Property="Cursor" Value="Hand"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Border Background="{TemplateBinding Background}" 
                                CornerRadius="8" 
                                Padding="{TemplateBinding Padding}">
                            <ContentPresenter HorizontalAlignment="Center" 
                                            VerticalAlignment="Center"/>
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter Property="Background" Value="{StaticResource PrimaryLightColor}"/>
                            </Trigger>
                            <Trigger Property="IsPressed" Value="True">
                                <Setter Property="Background" Value="{StaticResource PrimaryColor}"/>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>

        <Style x:Key="SecondaryButtonStyle" TargetType="Button">
            <Setter Property="Background" Value="Transparent"/>
            <Setter Property="Foreground" Value="{StaticResource PrimaryColor}"/>
            <Setter Property="BorderThickness" Value="2"/>
            <Setter Property="BorderBrush" Value="{StaticResource PrimaryColor}"/>
            <Setter Property="Padding" Value="16,8"/>
            <Setter Property="Margin" Value="4"/>
            <Setter Property="FontWeight" Value="SemiBold"/>
            <Setter Property="Cursor" Value="Hand"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Border Background="{TemplateBinding Background}" 
                                CornerRadius="8" 
                                BorderBrush="{TemplateBinding BorderBrush}"
                                BorderThickness="{TemplateBinding BorderThickness}"
                                Padding="{TemplateBinding Padding}">
                            <ContentPresenter HorizontalAlignment="Center" 
                                            VerticalAlignment="Center"/>
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter Property="Background" Value="{StaticResource PrimaryColor}"/>
                                <Setter Property="Foreground" Value="White"/>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>

        <!-- أنماط حقول الإدخال -->
        <Style x:Key="InputTextBoxStyle" TargetType="TextBox">
            <Setter Property="Padding" Value="12,8"/>
            <Setter Property="Margin" Value="0,4,0,16"/>
            <Setter Property="BorderThickness" Value="2"/>
            <Setter Property="BorderBrush" Value="#e2e8f0"/>
            <Setter Property="Background" Value="White"/>
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="TextBox">
                        <Border Background="{TemplateBinding Background}"
                                BorderBrush="{TemplateBinding BorderBrush}"
                                BorderThickness="{TemplateBinding BorderThickness}"
                                CornerRadius="8"
                                Padding="{TemplateBinding Padding}">
                            <ScrollViewer x:Name="PART_ContentHost"/>
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsFocused" Value="True">
                                <Setter Property="BorderBrush" Value="{StaticResource PrimaryColor}"/>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>

        <Style x:Key="InputComboBoxStyle" TargetType="ComboBox">
            <Setter Property="Padding" Value="12,8"/>
            <Setter Property="Margin" Value="0,4,0,16"/>
            <Setter Property="BorderThickness" Value="2"/>
            <Setter Property="BorderBrush" Value="#e2e8f0"/>
            <Setter Property="Background" Value="White"/>
            <Setter Property="FontSize" Value="14"/>
        </Style>

        <!-- أنماط التسميات -->
        <Style x:Key="LabelStyle" TargetType="TextBlock">
            <Setter Property="FontWeight" Value="SemiBold"/>
            <Setter Property="Foreground" Value="{StaticResource TextPrimaryColor}"/>
            <Setter Property="Margin" Value="0,0,0,4"/>
        </Style>

        <Style x:Key="RequiredLabelStyle" TargetType="TextBlock" BasedOn="{StaticResource LabelStyle}">
            <Setter Property="Foreground" Value="{StaticResource ErrorColor}"/>
        </Style>
    </Window.Resources>

    <Grid Background="{StaticResource BackgroundColor}">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- شريط العنوان -->
        <Border Grid.Row="0" Background="{StaticResource PrimaryColor}" Padding="20,16">
            <StackPanel Orientation="Horizontal">
                <TextBlock Text="➕" FontSize="24" Foreground="White" VerticalAlignment="Center" Margin="0,0,12,0"/>
                <TextBlock Text="إضافة مورد جديد" FontSize="20" FontWeight="Bold" Foreground="White" VerticalAlignment="Center"/>
            </StackPanel>
        </Border>

        <!-- محتوى النموذج -->
        <ScrollViewer Grid.Row="1" VerticalScrollBarVisibility="Auto" Padding="30">
            <StackPanel MaxWidth="600">
                
                <!-- المعلومات الأساسية -->
                <TextBlock Text="📋 المعلومات الأساسية" FontSize="18" FontWeight="Bold" 
                         Foreground="{StaticResource PrimaryColor}" Margin="0,0,0,20"/>

                <Grid>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="20"/>
                        <ColumnDefinition Width="*"/>
                    </Grid.ColumnDefinitions>

                    <StackPanel Grid.Column="0">
                        <TextBlock Text="اسم المورد *" Style="{StaticResource RequiredLabelStyle}"/>
                        <TextBox x:Name="TxtSupplierName" Style="{StaticResource InputTextBoxStyle}" 
                               ToolTip="أدخل اسم المورد الكامل"/>
                    </StackPanel>

                    <StackPanel Grid.Column="2">
                        <TextBlock Text="الاسم التجاري" Style="{StaticResource LabelStyle}"/>
                        <TextBox x:Name="TxtCommercialName" Style="{StaticResource InputTextBoxStyle}" 
                               ToolTip="أدخل الاسم التجاري للشركة"/>
                    </StackPanel>
                </Grid>

                <Grid>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="20"/>
                        <ColumnDefinition Width="*"/>
                    </Grid.ColumnDefinitions>

                    <StackPanel Grid.Column="0">
                        <TextBlock Text="رقم الرخصة التجارية *" Style="{StaticResource RequiredLabelStyle}"/>
                        <TextBox x:Name="TxtLicenseNumber" Style="{StaticResource InputTextBoxStyle}" 
                               ToolTip="أدخل رقم الرخصة التجارية"/>
                    </StackPanel>

                    <StackPanel Grid.Column="2">
                        <TextBlock Text="الشخص المسؤول" Style="{StaticResource LabelStyle}"/>
                        <TextBox x:Name="TxtContactPerson" Style="{StaticResource InputTextBoxStyle}" 
                               ToolTip="أدخل اسم الشخص المسؤول"/>
                    </StackPanel>
                </Grid>

                <!-- معلومات الاتصال -->
                <TextBlock Text="📞 معلومات الاتصال" FontSize="18" FontWeight="Bold" 
                         Foreground="{StaticResource PrimaryColor}" Margin="0,20,0,20"/>

                <Grid>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="20"/>
                        <ColumnDefinition Width="*"/>
                    </Grid.ColumnDefinitions>

                    <StackPanel Grid.Column="0">
                        <TextBlock Text="رقم الهاتف" Style="{StaticResource LabelStyle}"/>
                        <TextBox x:Name="TxtPhoneNumber" Style="{StaticResource InputTextBoxStyle}" 
                               ToolTip="أدخل رقم الهاتف"/>
                    </StackPanel>

                    <StackPanel Grid.Column="2">
                        <TextBlock Text="البريد الإلكتروني" Style="{StaticResource LabelStyle}"/>
                        <TextBox x:Name="TxtEmail" Style="{StaticResource InputTextBoxStyle}" 
                               ToolTip="أدخل البريد الإلكتروني"/>
                    </StackPanel>
                </Grid>

                <StackPanel>
                    <TextBlock Text="العنوان" Style="{StaticResource LabelStyle}"/>
                    <TextBox x:Name="TxtAddress" Style="{StaticResource InputTextBoxStyle}" 
                           Height="80" TextWrapping="Wrap" AcceptsReturn="True"
                           VerticalScrollBarVisibility="Auto"
                           ToolTip="أدخل العنوان الكامل"/>
                </StackPanel>

                <!-- فئة المورد -->
                <TextBlock Text="🏷️ تصنيف المورد" FontSize="18" FontWeight="Bold" 
                         Foreground="{StaticResource PrimaryColor}" Margin="0,20,0,20"/>

                <StackPanel>
                    <TextBlock Text="فئة المورد" Style="{StaticResource LabelStyle}"/>
                    <ComboBox x:Name="CmbCategory" Style="{StaticResource InputComboBoxStyle}" 
                            ToolTip="اختر فئة المورد"/>
                </StackPanel>

                <!-- ملاحظات -->
                <TextBlock Text="📝 ملاحظات إضافية" FontSize="18" FontWeight="Bold" 
                         Foreground="{StaticResource PrimaryColor}" Margin="0,20,0,20"/>

                <StackPanel>
                    <TextBlock Text="ملاحظات" Style="{StaticResource LabelStyle}"/>
                    <TextBox x:Name="TxtNotes" Style="{StaticResource InputTextBoxStyle}" 
                           Height="100" TextWrapping="Wrap" AcceptsReturn="True"
                           VerticalScrollBarVisibility="Auto"
                           ToolTip="أدخل أي ملاحظات إضافية"/>
                </StackPanel>

            </StackPanel>
        </ScrollViewer>

        <!-- أزرار الإجراءات -->
        <Border Grid.Row="2" Background="White" BorderBrush="#e2e8f0" BorderThickness="0,1,0,0" Padding="30,20">
            <StackPanel Orientation="Horizontal" HorizontalAlignment="Left">
                <Button x:Name="BtnSave" Content="💾 حفظ المورد" Style="{StaticResource PrimaryButtonStyle}" 
                      Click="BtnSave_Click" MinWidth="120"/>
                <Button x:Name="BtnCancel" Content="❌ إلغاء" Style="{StaticResource SecondaryButtonStyle}" 
                      Click="BtnCancel_Click" MinWidth="120"/>
            </StackPanel>
        </Border>

    </Grid>
</Window>
