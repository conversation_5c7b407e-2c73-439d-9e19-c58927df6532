using Microsoft.EntityFrameworkCore;
using SupplierManagementSystem.Data;
using SupplierManagementSystem.Models;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace SupplierManagementSystem.Services
{
    /// <summary>
    /// خدمة إدارة الموردين
    /// Supplier Management Service
    /// </summary>
    public class SupplierService
    {
        private readonly SupplierDbContext _context;

        public SupplierService(SupplierDbContext context)
        {
            _context = context;
        }

        // الحصول على جميع الموردين
        public async Task<List<Supplier>> GetAllSuppliersAsync()
        {
            return await _context.Suppliers
                .Include(s => s.Documents)
                .Include(s => s.AccreditationRecords)
                .Include(s => s.CategoryMappings)
                    .ThenInclude(cm => cm.Category)
                .Where(s => s.IsActive)
                .OrderBy(s => s.SupplierName)
                .ToListAsync();
        }

        // الحصول على مورد بالمعرف
        public async Task<Supplier?> GetSupplierByIdAsync(int id)
        {
            return await _context.Suppliers
                .Include(s => s.Documents)
                    .ThenInclude(d => d.DocumentType)
                .Include(s => s.AccreditationRecords)
                    .ThenInclude(ar => ar.Reviewer)
                .Include(s => s.Evaluations)
                    .ThenInclude(e => e.Evaluator)
                .Include(s => s.CategoryMappings)
                    .ThenInclude(cm => cm.Category)
                .Include(s => s.ActivityLogs)
                .FirstOrDefaultAsync(s => s.SupplierID == id && s.IsActive);
        }

        // البحث في الموردين
        public async Task<List<Supplier>> SearchSuppliersAsync(string searchTerm, string? status = null)
        {
            var query = _context.Suppliers
                .Include(s => s.Documents)
                .Include(s => s.CategoryMappings)
                    .ThenInclude(cm => cm.Category)
                .Where(s => s.IsActive);

            if (!string.IsNullOrEmpty(searchTerm))
            {
                query = query.Where(s => 
                    s.SupplierName.Contains(searchTerm) ||
                    s.CommercialName.Contains(searchTerm) ||
                    s.LicenseNumber.Contains(searchTerm) ||
                    s.ContactPerson.Contains(searchTerm));
            }

            if (!string.IsNullOrEmpty(status))
            {
                query = query.Where(s => s.Status == status);
            }

            return await query.OrderBy(s => s.SupplierName).ToListAsync();
        }

        // إضافة مورد جديد
        public async Task<Supplier> AddSupplierAsync(Supplier supplier, string createdBy)
        {
            supplier.CreatedBy = createdBy;
            supplier.ModifiedBy = createdBy;
            supplier.CreatedDate = DateTime.Now;
            supplier.ModifiedDate = DateTime.Now;

            _context.Suppliers.Add(supplier);
            await _context.SaveChangesAsync();

            // إضافة سجل نشاط
            await AddActivityLogAsync(supplier.SupplierID, ActivityType.Create, 
                $"تم إنشاء مورد جديد: {supplier.SupplierName}", createdBy);

            return supplier;
        }

        // تحديث مورد
        public async Task<Supplier> UpdateSupplierAsync(Supplier supplier, string modifiedBy)
        {
            var existingSupplier = await _context.Suppliers.FindAsync(supplier.SupplierID);
            if (existingSupplier == null)
                throw new ArgumentException("المورد غير موجود");

            // تحديث البيانات
            existingSupplier.SupplierName = supplier.SupplierName;
            existingSupplier.CommercialName = supplier.CommercialName;
            existingSupplier.LicenseNumber = supplier.LicenseNumber;
            existingSupplier.ContactPerson = supplier.ContactPerson;
            existingSupplier.PhoneNumber = supplier.PhoneNumber;
            existingSupplier.Email = supplier.Email;
            existingSupplier.Address = supplier.Address;
            existingSupplier.Notes = supplier.Notes;
            existingSupplier.ModifiedBy = modifiedBy;
            existingSupplier.ModifiedDate = DateTime.Now;

            await _context.SaveChangesAsync();

            // إضافة سجل نشاط
            await AddActivityLogAsync(supplier.SupplierID, ActivityType.Update,
                $"تم تحديث بيانات المورد: {existingSupplier.SupplierName}", modifiedBy);

            return existingSupplier;
        }

        // تحديث حالة المورد
        public async Task<bool> UpdateSupplierStatusAsync(int supplierId, string newStatus, 
            int? reviewerId, string notes, string updatedBy)
        {
            var supplier = await _context.Suppliers.FindAsync(supplierId);
            if (supplier == null) return false;

            var oldStatus = supplier.Status;
            supplier.Status = newStatus;
            supplier.ModifiedBy = updatedBy;
            supplier.ModifiedDate = DateTime.Now;

            // إضافة سجل اعتماد
            var accreditationRecord = new AccreditationRecord
            {
                SupplierID = supplierId,
                ReviewerID = reviewerId,
                PreviousStatus = oldStatus,
                NewStatus = newStatus,
                Decision = GetDecisionFromStatus(newStatus),
                ReviewNotes = notes,
                ReviewDate = DateTime.Now
            };

            _context.AccreditationRecords.Add(accreditationRecord);
            await _context.SaveChangesAsync();

            // إضافة سجل نشاط
            await AddActivityLogAsync(supplierId, ActivityType.StatusUpdate, 
                $"تم تغيير حالة المورد من {oldStatus} إلى {newStatus}", updatedBy);

            return true;
        }

        // حذف مورد (حذف منطقي)
        public async Task<bool> DeleteSupplierAsync(int id, string deletedBy)
        {
            var supplier = await _context.Suppliers.FindAsync(id);
            if (supplier == null) return false;

            var supplierName = supplier.SupplierName;
            supplier.IsActive = false;
            supplier.ModifiedBy = deletedBy;
            supplier.ModifiedDate = DateTime.Now;

            await _context.SaveChangesAsync();

            // إضافة سجل نشاط
            await AddActivityLogAsync(id, ActivityType.Delete,
                $"تم حذف المورد: {supplierName}", deletedBy);

            return true;
        }

        // الحصول على إحصائيات الموردين
        public async Task<Dictionary<string, int>> GetSupplierStatisticsAsync()
        {
            var statistics = await _context.Suppliers
                .Where(s => s.IsActive)
                .GroupBy(s => s.Status)
                .Select(g => new { Status = g.Key, Count = g.Count() })
                .ToDictionaryAsync(x => x.Status, x => x.Count);

            return statistics;
        }

        // الحصول على الوثائق المنتهية الصلاحية
        public async Task<List<SupplierDocument>> GetExpiringDocumentsAsync(int daysAhead = 30)
        {
            var futureDate = DateTime.Today.AddDays(daysAhead);
            
            return await _context.SupplierDocuments
                .Include(d => d.Supplier)
                .Include(d => d.DocumentType)
                .Where(d => d.IsActive && 
                           d.ExpiryDate.HasValue && 
                           d.ExpiryDate.Value >= DateTime.Today && 
                           d.ExpiryDate.Value <= futureDate)
                .OrderBy(d => d.ExpiryDate)
                .ToListAsync();
        }

        // الحصول على الموردين حسب الحالة
        public async Task<List<Supplier>> GetSuppliersByStatusAsync(string status)
        {
            return await _context.Suppliers
                .Include(s => s.Documents)
                .Include(s => s.CategoryMappings)
                    .ThenInclude(cm => cm.Category)
                .Where(s => s.IsActive && s.Status == status)
                .OrderBy(s => s.SupplierName)
                .ToListAsync();
        }

        // إضافة سجل نشاط
        private async Task AddActivityLogAsync(int? supplierId, string activityType, 
            string description, string performedBy)
        {
            var activityLog = new ActivityLog
            {
                SupplierID = supplierId,
                ActivityType = activityType,
                ActivityDescription = description,
                PerformedBy = performedBy,
                PerformedDate = DateTime.Now
            };

            _context.ActivityLogs.Add(activityLog);
            await _context.SaveChangesAsync();
        }

        // تحديد القرار من الحالة
        private string GetDecisionFromStatus(string status)
        {
            return status switch
            {
                "معتمد" => AccreditationDecision.Approve,
                "مرفوض" => AccreditationDecision.Reject,
                "قيد المراجعة" => AccreditationDecision.Postpone,
                _ => AccreditationDecision.Postpone
            };
        }

        // التحقق من وجود رقم رخصة
        public async Task<bool> IsLicenseNumberExistsAsync(string licenseNumber, int? excludeId = null)
        {
            var query = _context.Suppliers.Where(s => s.LicenseNumber == licenseNumber && s.IsActive);
            
            if (excludeId.HasValue)
                query = query.Where(s => s.SupplierID != excludeId.Value);

            return await query.AnyAsync();
        }

        // الحصول على آخر النشاطات
        public async Task<List<ActivityLog>> GetRecentActivitiesAsync(int count = 10)
        {
            return await _context.ActivityLogs
                .Include(a => a.Supplier)
                .OrderByDescending(a => a.PerformedDate)
                .Take(count)
                .ToListAsync();
        }
    }
}
