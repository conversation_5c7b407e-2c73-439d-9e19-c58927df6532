using Microsoft.EntityFrameworkCore;
using SupplierManagementSystem.Data;
using SupplierManagementSystem.Models;
using SupplierManagementSystem.Services;
using System;
using System.Linq;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;

namespace SupplierManagementSystem.Views;

/// <summary>
/// نافذة إضافة مورد جديد
/// Add New Supplier Window
/// </summary>
public partial class AddSupplierWindow : Window
{
    private readonly SupplierService _supplierService;
    private readonly SupplierDbContext _context;

    public AddSupplierWindow(SupplierService supplierService)
    {
        InitializeComponent();
        _supplierService = supplierService;
        
        // إنشاء سياق قاعدة البيانات للحصول على الفئات
        var connectionString = "Data Source=SupplierManagement.db";
        var optionsBuilder = new DbContextOptionsBuilder<SupplierDbContext>();
        optionsBuilder.UseSqlite(connectionString);
        _context = new SupplierDbContext(optionsBuilder.Options);
        
        Loaded += AddSupplierWindow_Loaded;
    }

    private async void AddSupplierWindow_Loaded(object sender, RoutedEventArgs e)
    {
        try
        {
            await LoadCategoriesAsync();
        }
        catch (Exception ex)
        {
            MessageBox.Show($"خطأ في تحميل البيانات: {ex.Message}", "خطأ", 
                MessageBoxButton.OK, MessageBoxImage.Error);
        }
    }

    private async Task LoadCategoriesAsync()
    {
        try
        {
            var categories = await _context.SupplierCategories
                .Where(c => c.IsActive)
                .OrderBy(c => c.CategoryName)
                .ToListAsync();

            CmbCategory.ItemsSource = categories;
            CmbCategory.DisplayMemberPath = "CategoryName";
            CmbCategory.SelectedValuePath = "CategoryID";
            
            if (categories.Any())
                CmbCategory.SelectedIndex = 0;
        }
        catch (Exception ex)
        {
            MessageBox.Show($"خطأ في تحميل الفئات: {ex.Message}", "خطأ", 
                MessageBoxButton.OK, MessageBoxImage.Warning);
        }
    }

    private async void BtnSave_Click(object sender, RoutedEventArgs e)
    {
        try
        {
            // التحقق من صحة البيانات
            if (!ValidateInput())
                return;

            // تعطيل الأزرار أثناء الحفظ
            BtnSave.IsEnabled = false;
            BtnCancel.IsEnabled = false;
            BtnSave.Content = "⏳ جاري الحفظ...";

            // التحقق من عدم تكرار رقم الرخصة
            var licenseExists = await _supplierService.IsLicenseNumberExistsAsync(TxtLicenseNumber.Text.Trim());
            if (licenseExists)
            {
                MessageBox.Show("رقم الرخصة التجارية موجود مسبقاً. يرجى إدخال رقم مختلف.", "خطأ في البيانات", 
                    MessageBoxButton.OK, MessageBoxImage.Warning);
                
                TxtLicenseNumber.Focus();
                TxtLicenseNumber.SelectAll();
                return;
            }

            // إنشاء كائن المورد الجديد
            var newSupplier = new Supplier
            {
                SupplierName = TxtSupplierName.Text.Trim(),
                CommercialName = string.IsNullOrWhiteSpace(TxtCommercialName.Text) ? null : TxtCommercialName.Text.Trim(),
                LicenseNumber = TxtLicenseNumber.Text.Trim(),
                ContactPerson = string.IsNullOrWhiteSpace(TxtContactPerson.Text) ? null : TxtContactPerson.Text.Trim(),
                PhoneNumber = string.IsNullOrWhiteSpace(TxtPhoneNumber.Text) ? null : TxtPhoneNumber.Text.Trim(),
                Email = string.IsNullOrWhiteSpace(TxtEmail.Text) ? null : TxtEmail.Text.Trim(),
                Address = string.IsNullOrWhiteSpace(TxtAddress.Text) ? null : TxtAddress.Text.Trim(),
                Notes = string.IsNullOrWhiteSpace(TxtNotes.Text) ? null : TxtNotes.Text.Trim(),
                Status = SupplierStatus.New,
                CreatedDate = DateTime.Now,
                ModifiedDate = DateTime.Now,
                CreatedBy = "المستخدم الحالي", // يمكن تحديث هذا لاحقاً
                ModifiedBy = "المستخدم الحالي",
                IsActive = true
            };

            // حفظ المورد
            var savedSupplier = await _supplierService.AddSupplierAsync(newSupplier, "المستخدم الحالي");

            // إضافة فئة المورد إذا تم اختيارها
            if (CmbCategory.SelectedValue != null)
            {
                var categoryMapping = new SupplierCategoryMapping
                {
                    SupplierID = savedSupplier.SupplierID,
                    CategoryID = (int)CmbCategory.SelectedValue,
                    AssignedDate = DateTime.Now,
                    AssignedBy = "المستخدم الحالي",
                    IsActive = true
                };

                _context.SupplierCategoryMappings.Add(categoryMapping);
                await _context.SaveChangesAsync();
            }

            MessageBox.Show($"تم إضافة المورد '{savedSupplier.SupplierName}' بنجاح!", "تم الحفظ", 
                MessageBoxButton.OK, MessageBoxImage.Information);

            DialogResult = true;
            Close();
        }
        catch (Exception ex)
        {
            MessageBox.Show($"خطأ في حفظ المورد: {ex.Message}", "خطأ", 
                MessageBoxButton.OK, MessageBoxImage.Error);
        }
        finally
        {
            // إعادة تفعيل الأزرار
            BtnSave.IsEnabled = true;
            BtnCancel.IsEnabled = true;
            BtnSave.Content = "💾 حفظ المورد";
        }
    }

    private void BtnCancel_Click(object sender, RoutedEventArgs e)
    {
        var result = MessageBox.Show("هل أنت متأكد من إلغاء إضافة المورد؟ سيتم فقدان جميع البيانات المدخلة.", 
            "تأكيد الإلغاء", MessageBoxButton.YesNo, MessageBoxImage.Question);

        if (result == MessageBoxResult.Yes)
        {
            DialogResult = false;
            Close();
        }
    }

    private bool ValidateInput()
    {
        // التحقق من اسم المورد
        if (string.IsNullOrWhiteSpace(TxtSupplierName.Text))
        {
            MessageBox.Show("يرجى إدخال اسم المورد.", "خطأ في البيانات", 
                MessageBoxButton.OK, MessageBoxImage.Warning);
            TxtSupplierName.Focus();
            return false;
        }

        // التحقق من رقم الرخصة
        if (string.IsNullOrWhiteSpace(TxtLicenseNumber.Text))
        {
            MessageBox.Show("يرجى إدخال رقم الرخصة التجارية.", "خطأ في البيانات", 
                MessageBoxButton.OK, MessageBoxImage.Warning);
            TxtLicenseNumber.Focus();
            return false;
        }

        // التحقق من صحة البريد الإلكتروني إذا تم إدخاله
        if (!string.IsNullOrWhiteSpace(TxtEmail.Text))
        {
            try
            {
                var addr = new System.Net.Mail.MailAddress(TxtEmail.Text.Trim());
                if (addr.Address != TxtEmail.Text.Trim())
                {
                    throw new FormatException();
                }
            }
            catch
            {
                MessageBox.Show("البريد الإلكتروني غير صحيح. يرجى إدخال بريد إلكتروني صالح.", "خطأ في البيانات", 
                    MessageBoxButton.OK, MessageBoxImage.Warning);
                TxtEmail.Focus();
                TxtEmail.SelectAll();
                return false;
            }
        }

        // التحقق من طول البيانات
        if (TxtSupplierName.Text.Trim().Length > 200)
        {
            MessageBox.Show("اسم المورد يجب أن يكون أقل من 200 حرف.", "خطأ في البيانات", 
                MessageBoxButton.OK, MessageBoxImage.Warning);
            TxtSupplierName.Focus();
            return false;
        }

        if (TxtLicenseNumber.Text.Trim().Length > 50)
        {
            MessageBox.Show("رقم الرخصة يجب أن يكون أقل من 50 حرف.", "خطأ في البيانات", 
                MessageBoxButton.OK, MessageBoxImage.Warning);
            TxtLicenseNumber.Focus();
            return false;
        }

        return true;
    }

    protected override void OnClosed(EventArgs e)
    {
        _context?.Dispose();
        base.OnClosed(e);
    }
}
